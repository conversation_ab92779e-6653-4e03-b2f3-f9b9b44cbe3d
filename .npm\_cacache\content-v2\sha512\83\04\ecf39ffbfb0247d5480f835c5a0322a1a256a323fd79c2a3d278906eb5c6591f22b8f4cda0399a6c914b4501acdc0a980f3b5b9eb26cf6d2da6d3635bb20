{"_id": "chokidar", "_rev": "426-71bc4b90d3f3fbe3d7ef36534ecd360b", "name": "chokidar", "dist-tags": {"latest": "4.0.3"}, "versions": {"0.1.1": {"name": "chokidar", "version": "0.1.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.1.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "874eb8ccf2d3e17db63224ffde23f9134f80f7d5", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.1.1.tgz", "integrity": "sha512-rbn5wcfS9YSfR6XjSB4A2l0pgzx+gYEcKYdwPPO1Je8B0/59l/UWj5+ZJskHqgC9U1zZ3rOiSsPutJdvSqtGsw==", "signatures": [{"sig": "MEUCICIrAynOiglC7h64ZjhXS+0GFT7FcTaQgT9/H0prCvWoAiEAsZgWdWMK/cHaktOJefQ74Qrzl1wXQwzsGC3/COv2G3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "scripts": {"test": "node beare.js testWithMocha", "postinstall": "node beare.js compileCoffeeScripts"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "repository": {"url": "git://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.1.18", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {"beare": "0.1.1"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.0": {"name": "chokidar", "version": "0.2.0", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.2.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "7c585f4d9dfb772669db81677d86530b87efff05", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.2.0.tgz", "integrity": "sha512-xDR8wjY422lXievx5wQOu3f4hjan09ChofXz4DOfv6KDDCkDq0pgHxKsF+mGbFve4szrEF1Cb8cROZhOqBZn6w==", "signatures": [{"sig": "MEUCIQDGbNY/f/mYUq2s3Ptlt+F8XFbRksL72NqaxlZysrcyKQIgYCf1fADEsvJDt8pQQbU6+xPDMyoJXemX3j7czH9y67E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "git://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.1.18", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.1": {"name": "chokidar", "version": "0.2.1", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.2.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "ea10091cdd6c413253d8f3a83db6e775d96eddbc", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.2.1.tgz", "integrity": "sha512-HUCF3BavfQAC771111yzh8HVIOpNAxbauEZOiUaHH5s2ODF7qiAngO2fXnIXrv/C5MZ7tkamHbV+GkHH66nOOg==", "signatures": [{"sig": "MEQCIGXGH9laHfSDz/trEDBjpAMTmxvmLqCz0qJnCWRdOx9JAiBq5bSsEquIX4uUhe8716AHPypRo21p/GsYV5I+SKoScw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "git://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.1.18", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.2": {"name": "chokidar", "version": "0.2.2", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.2.2", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "3dfa716e7a5a55e503a77004f64e758f86124526", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.2.2.tgz", "integrity": "sha512-5Y47XyG1a8vs4a4tCE6l/OINik7fBsvunNC2bhy+S86G2kFUgSs4P6L9mhGRAB7gzRpqaf4wbqWqCIjZjFYvxQ==", "signatures": [{"sig": "MEUCIQC2Jn4zFPkLMh9oCnA3FXp4Z98SDhmDJvHPgIVXpKUlmAIgLTnukp87Ocyfqzl5a/vDFB3s6kWBU0r2MwTt35xbtLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "git://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.1.18", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.3": {"name": "chokidar", "version": "0.2.3", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.2.3", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "1f322a97518fe051e07fc6fb2ac576bcdb208be6", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.2.3.tgz", "integrity": "sha512-qNFu+UewuesXifi88LJ6fvQoH6kW8RlCN26x8vb8aGcsS/bU14mMHcX2pptS/FeWH19K4Fgi8xfBmiBx/nhXuw==", "signatures": [{"sig": "MEYCIQDZxpdfAlrFqFJGxkYVoxUJ453lLw2LaQs6z/8kI+fENAIhAPmRs38Elqpdiht+D98TRNH0ShbvgLo5bohX+eJbuid9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "git://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.1.18", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.4": {"name": "chokidar", "version": "0.2.4", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.2.4", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "aa6df18b362a4254990dcd28014630a1553155e1", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.2.4.tgz", "integrity": "sha512-ZBD9QcTixNgpsrr28FR0XjntsfboEXqYIvJTd5ZjHH+/R3gtqq8ReelTklIzvH7lqiEglPaKoMuJYhOzomdnIg==", "signatures": [{"sig": "MEUCIHL0/d7pNCuLDAvUdqOrEFfdDgmbCLHU98nkn5iM/vMUAiEAvrOZAPoDjjlKijIOlFChMXA3gPYVI7jVLIh62v6htZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "git://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.1.18", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.5": {"name": "chokidar", "version": "0.2.5", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.2.5", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "4228827b67e28e01cbacb34925effd8768f437cc", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.2.5.tgz", "integrity": "sha512-5Ihb9UiEfSekfq76yDBWuA8RAtk2LillUltR0vBSEjVvozr6x2qFGgQlH9OHmFCArxQ+Z8b86Tnn2klu3b50OQ==", "signatures": [{"sig": "MEUCIAWOpnb9QC7Rjx469jBYVaYVJk/5gduUubN4UmTPtng9AiEAi3vbEfUNlIUUfoygaICivkr1xNd3wh/GG0SR9e/HeCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "git://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.1.18", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.6": {"name": "chokidar", "version": "0.2.6", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.2.6", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "852022031f1fc1feb54af2fa0b5efe527370d454", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.2.6.tgz", "integrity": "sha512-vhvUE6i1ua6DfjKdp/jDp0fCJFWEuwlkn1sLtv5szJn38WxBYEeO6OJFjk1OmN8ofd7Qj364FXRUW63DU2mpig==", "signatures": [{"sig": "MEUCIC+WiWETv60I7RJp55tUFlMcJusfN/6tD7+TEBD23DmBAiEArUsONqoiQqhi4LH6xQUHkAdRQogOmqNgs3IuXubQsZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "git://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.1.18", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}, "_engineSupported": true, "optionalDependencies": {}}, "0.3.0": {"name": "chokidar", "version": "0.3.0", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.3.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "153d3aa76231b47c7981ae8163aa141cf9c27009", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.3.0.tgz", "integrity": "sha512-nW4UtH0vm0HIL8+abag7QTh0fBcffGK5H0MdBvCzbATO9R2exY2AOMDFG39U3FrGHH5Qi1+5xGLLQKbNytBKZg==", "signatures": [{"sig": "MEQCIC4N2xu4D2UxHyQehYHdGCWtpfXTwikZd2+EakbyhYg3AiAOAPeo0jS4PKNiiiI3TMrttyQdyIj6roKXhACoMj3yAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "scripts": {"test": "node setup.js test", "prepublish": "coffee -o lib/ src/", "postinstall": "node setup.js postinstall"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "description": "A neat wrapper around node.js fs.watch / fs.watchFile", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "1.0.1", "expect.js": "0.1.2"}}, "0.4.0": {"name": "chokidar", "version": "0.4.0", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.4.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "4d8164c8288b930b62e7f1515a37e146329d26d3", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.4.0.tgz", "integrity": "sha512-g9+cz4Ze3hjSEILSLKPUWeBoKkjRe1UagzguM3hIC96g/0SToL+BSA8EtAQWZLgtUXiHnEW4ztzZZ6+KjykZOg==", "signatures": [{"sig": "MEUCIQCdZs4VlCjprUhXZ5go5hQNsFNDej6GGQdHFrDycCK6dQIgCc79bVa0C3UnyI2WkK6nCqNdIEbkzenKMxmF3E8I4PA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "scripts": {"test": "node setup.js test", "prepublish": "coffee -o lib/ src/", "postinstall": "node setup.js postinstall"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "1.3.0", "expect.js": "0.1.2"}}, "0.5.0": {"name": "chokidar", "version": "0.5.0", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.5.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "0093837680c05f8268f868cb96ac87b0bc78c872", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.5.0.tgz", "integrity": "sha512-WqHdExtgTA4GA2FDKpNmSJJMoGtW9uce6Q3UFZFEqhpgZkDXFG/jbOuSRrXVruw0nQfRUA0VxqZN7lqNQu0EUg==", "signatures": [{"sig": "MEYCIQDGQDScrFBXs/IDtsy6Tfqhh6RhSqpb/GQIS3EmS9LBrAIhAP+GdXI/LoxJQKUu0nr+3wrV7xl/Cj+OWr/q4jhsKiS8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "scripts": {"test": "node setup.js test", "prepublish": "coffee -o lib/ src/", "postinstall": "node setup.js postinstall"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.4.0"}}, "0.5.1": {"name": "chokidar", "version": "0.5.1", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.5.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "097e679d96a31cb1d227c14c97aa7650b9913de2", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.5.1.tgz", "integrity": "sha512-G4jWBIhBMHdxMljQxb++O9iseG9P+75/Gn2X9oAUr7tfHY+eIE4fQ5jnfmXgnMR1lRjOB4Z5DkIjzBLSH+CMXw==", "signatures": [{"sig": "MEUCIFbUj4nn1+wZpRnJWZRyB5LNTReJgvItg5G5JwHn7g9BAiEAhXRoBszwaAp16Vnb+TfnhTw7wpEN9NYahATYPGJAYMU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8"}, "scripts": {"test": "node setup.js test", "prepublish": "coffee -o lib/ src/", "postinstall": "node setup.js postinstall"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.4.0"}}, "0.5.2": {"name": "chokidar", "version": "0.5.2", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.5.2", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "867e9e2ecece696897ac7bf4a656731d4e79b860", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.5.2.tgz", "integrity": "sha512-9RE<PERSON>yyebUDNBDluraxezEOWfkpBrBn+2fhUI1Btf16hxNDql7gRcy2qQYfclpbPc+xJh/QCC9HPgTpClRX+2Og==", "signatures": [{"sig": "MEQCIHNCFAnZmZ5eI1KRCgVJgfWCF+WzipVPyz/OeOGaxyNJAiBOQxRJ8N1aOaoH6+6uTqa/cN/hshyfNrHn73Y0PuLckg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8 || 0.9"}, "scripts": {"test": "node setup.js test", "prepublish": "node setup.js prepublish", "postpublish": "node setup.js postpublish"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.4.0"}}, "0.5.3": {"name": "chokidar", "version": "0.5.3", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.5.3", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "3c5290279c4ec552466671988b76e2bc10c39191", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.5.3.tgz", "integrity": "sha512-2mV8OxfkhQDPUiaNaLgcKKjMRTe5NIKFg2rnzIJvKWe839BON0Ore/ZmjEUjy5+6AbYUhSLMury3UCm2CLZ0tQ==", "signatures": [{"sig": "MEUCIDM1NexliySHl6OFrYK2p6OifPTlgmMWeLVc1Q5T0FKtAiEAtm29Qp4zyJ4niSNDoZ/ADMvVkVLt5vI/QpM27oF6PY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "engines": {"node": "~0.6.10 || 0.7 || 0.8 || 0.9"}, "scripts": {"test": "node setup.js test", "prepublish": "node setup.js prepublish", "postpublish": "node setup.js postpublish"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.4.0"}}, "0.6.0": {"name": "chokidar", "version": "0.6.0", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.6.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "74df31f575676bf2053576e523b0d52bbca84cad", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.6.0.tgz", "integrity": "sha512-IBQcJqANIrauuyWwEdVktBUpX6VNQFsPWqDMZnESoz2UECyTa5lrBOCpQh9Cl/7ucfsDQ81TUnEV7TPtxV2rsg==", "signatures": [{"sig": "MEQCIBAluAxjobMBY4vSlf1W6+G3Fi2pyQLv7/CF1xBspqX0AiAOGT+szDoSlkCBQpsS3AzVaUTYcOEdgAyXh6qa10SC0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "_from": ".", "engines": {"node": "~0.6.10 || 0.7 || 0.8 || 0.9"}, "scripts": {"test": "mocha --compilers coffee:coffee-script --require test/common.js --colors", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.4.0"}}, "0.6.1": {"name": "chokidar", "version": "0.6.1", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.6.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "a2042d38e08b2b9b85bcab62b51eb59aa31f8364", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.6.1.tgz", "integrity": "sha512-bh3ReqqhhnsJdP5JkDMcJ1QKa+IZfSOFUah54v7la4OPiIBBHd//FeQH8xSRw2fxaloMXZk4oAZ5B/HperOA4Q==", "signatures": [{"sig": "MEQCIFa+44FpiC7SKO93RmvpQIdwFpPqyLm/wgiaABraqkz+AiBgOVC/IN2bBjroMcZq9anvLzvOrAholhKY1ma+VGMJ7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "_from": ".", "scripts": {"test": "mocha --compilers coffee:coffee-script --require test/common.js --colors", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}}, "0.6.2": {"name": "chokidar", "version": "0.6.2", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.6.2", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "827ca4aba5f88198037d8e369d206b44b38e17ca", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.6.2.tgz", "integrity": "sha512-NZlEhCCJU3BUGhbxyir1oP30KZE8DGXRfOyxJZDZKkc3pKXNrpcx3wAqkVsKMwUi3/YxRWAoQCa5YQ6EQy3u6w==", "signatures": [{"sig": "MEQCIG4OWMELFsXNcyNXHAIz1syuhBA9KNuD9VhU97Z7IQBGAiAO7nzM4TRPzmnu9sGGlBlPKwddpEbiMwmdOAh7MdbGug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "_from": ".", "scripts": {"test": "mocha --compilers coffee:coffee-script --require test/common.js --colors", "prepublish": "coffee -o lib/ src/"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}}, "0.6.3": {"name": "chokidar", "version": "0.6.3", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.6.3", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "e85968fa235f21773d388c617af085bf2104425a", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.6.3.tgz", "integrity": "sha512-IV+veAC39dHzegDTA7fib37BRGGw3ywg9HAtpJnpaYgZRnUvSlgdgFK3yKYdkHCUnsOvoZeOD/KRMgQkY+FyUQ==", "signatures": [{"sig": "MEUCIFCmAOeL+B67fsJmFa5d7k5Hd1boqRVNXk4SfBsdVHvlAiEA2Zb4eUHVHBKMm/8ohnJgDwS0LHQzWkKjCppgkI4goBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "_from": ".", "scripts": {"test": "node setup.js test", "prepublish": "node setup.js prepublish", "postpublish": "node setup.js postpublish"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}}, "0.7.0": {"name": "chokidar", "version": "0.7.0", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.7.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "bfaa9bdf30c0921dbe0a98bd93a3e06a5d5814e5", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.7.0.tgz", "integrity": "sha512-DAHbgvcg59rkY/OmglFyLrlb47w96W3u3Epma2wgTS4Mcua9dSQRmMSdYGYlN3ESU+7qGXMeJucPB6261IyapQ==", "signatures": [{"sig": "MEUCIQD38vReSNwj4cVN6GKlVUf7Vrn6Da7l0umb244EzP45oQIgSiuiJmDMSYU7Ymzj9lSYdD1MADRmC8mnz4f6Msz1oZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "_from": ".", "scripts": {"test": "node setup.js test", "prepublish": "node setup.js prepublish", "postinstall": "node setup.js postinstall", "postpublish": "node setup.js postpublish"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}}, "0.7.1": {"name": "chokidar", "version": "0.7.1", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.7.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "a5a5b2d5df265f96d90b9888f45a9e604254112c", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.7.1.tgz", "integrity": "sha512-zF2jwdBLDRVey6RtGDU1zn6C3hG1XMtA6u2qFSzRraMOBfk0TDmra0jZsOZGfXyiOS1n/FlNgIxMXdKLxRUj0w==", "signatures": [{"sig": "MEQCIGw+MxWyt+G/qlIxSxhKJ9OQO/UgBqwNDv/zyfsf2LGZAiBRsYghJvrhrhFvkjI0KWHkJOoKjec1gPTWEgifFqiWDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "_from": ".", "scripts": {"test": "node setup.js test", "prepublish": "node setup.js prepublish", "postinstall": "node setup.js postinstall", "postpublish": "node setup.js postpublish"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}}, "0.8.0": {"name": "chokidar", "version": "0.8.0", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.8.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "13af7baabeeb7aef71bb14132f9eacfd5abaed66", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.8.0.tgz", "integrity": "sha512-7pCMVc2Sacy7+Lxx3iaoWP6glKsrY96nTHVGH9XOGlpbyb9kN1KIWQztPrkwhZgTUSLyWMzP9WW7jQtR5a4+YA==", "signatures": [{"sig": "MEUCIQDgpJAwkbFXbho9swPHyS3Zg/itNtnRsgztLjjaznGGIAIgVtvwNrqwOlukYMnBFwVJw5ZK39/ST7sV5kgy3ODVDL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index", "_from": ".", "scripts": {"test": "node setup.js test", "prepublish": "node setup.js prepublish", "postpublish": "node setup.js postpublish"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {"fsevents": "0.1.6", "recursive-readdir": "0.0.2"}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "rimraf": "~2.2.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}, "optionalDependencies": {"fsevents": "0.1.6", "recursive-readdir": "0.0.2"}}, "0.8.1": {"name": "chokidar", "version": "0.8.1", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.8.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "8ee0c99ef48420902fded73b862eac2dd75da4a6", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.8.1.tgz", "integrity": "sha512-Bl/CteHnM5g3eKGPMkraZQW7Yzk2Gu87eE3jdaMfCNJP79sVa54M5KHStr3WtJl4vVoVSndVDws6IFLTmMa3Lw==", "signatures": [{"sig": "MEUCIQCI4/+a+Zxw6iIIbu9BdHT3J1pnl9WCvgNUgIWh/gT/dgIgeVnvRw9IMEdQoBN35NPFQwtUvAVMXkfKDw4NnpKpF5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "./node_modules/.bin/mocha", "postinstall": "node setup-deps.js"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "rimraf": "~2.2.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}}, "0.8.2": {"name": "chokidar", "version": "0.8.2", "keywords": ["fs", "watch", "watchFile", "watcher", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.8.2", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "767e2509aaa040fd8a23cc46225a783dc1bfc899", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.8.2.tgz", "integrity": "sha512-wJaDLi7IiYOeFZhP2zPjwMqD69E8zpPOyq/rXTrB79mWZtzIwh9JFEE7hXOcWG5qEJp+px8ADCn53Iyz5ulT/g==", "signatures": [{"sig": "MEUCICZFIzeqKnz0pPLF7higwZpRRU/9vOiLMnCcZZy6K1IKAiEA8B7vc2T2fZGsiICQr4uydcN/CexamLQ0TeXGcRPnITk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {"fsevents": "0.2.0", "recursive-readdir": "0.0.2"}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "rimraf": "~2.2.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}, "optionalDependencies": {"fsevents": "0.2.0", "recursive-readdir": "0.0.2"}}, "0.8.3": {"name": "chokidar", "version": "0.8.3", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.8.3", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "2d8da709a8fe3f6072c429737d7d0175fb81bef9", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.8.3.tgz", "integrity": "sha512-2jbJx3mfXmW4QxzEirX2bmNYU0CmzODrZ78gdgFukTGxKYb+MmdGCd7v2W3Ir6Up2Q3longaZb4MCtTUNuHpBQ==", "signatures": [{"sig": "MEUCIBpL6JXgjQCIi2vq2hYHFW/s4iW/3gMJ+rY+cGN3eR+JAiEAsU+o2tB5dV5CS66/cUBtQmgBQ8bG2ZVVkMHRRLl6SaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "2d8da709a8fe3f6072c429737d7d0175fb81bef9", "gitHead": "80037cf2f94402e1c01412f385756e5bbd7b4eb5", "scripts": {"test": "mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.0.0-alpha-5", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {"fsevents": "0.2.0", "recursive-readdir": "0.0.2"}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "rimraf": "~2.2.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}, "optionalDependencies": {"fsevents": "0.2.0", "recursive-readdir": "0.0.2"}}, "0.8.4": {"name": "chokidar", "version": "0.8.4", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.8.4", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "3b2b5066817086534ba81a092bdcf4be25b8bee0", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.8.4.tgz", "integrity": "sha512-auWFkYQV6w4821+YQATR3ueTMRPMAK9qVzl9cyE1O7dM7l2/NI6IIDcvPrER+RzdgoCi5b9aCy3btoZFsLVJbw==", "signatures": [{"sig": "MEYCIQDysLeCo0js1XHuZ3ttTa/U/r398lNiVBoeeuHKSto8YAIhAJ4RQjzo1aa+9N5Ih0g0+uBWZeQol/UNuR74zZnjX9S3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "3b2b5066817086534ba81a092bdcf4be25b8bee0", "gitHead": "67c8c5330ed12cbbcd885409d899f86195dbe802", "scripts": {"test": "mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.0.0-alpha-5", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {"fsevents": "pipobscure/fsevents#7dcdf9fa3f8956610fd6f69f72c67bace2de7138", "recursive-readdir": "0.0.2"}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "rimraf": "~2.2.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}, "optionalDependencies": {"fsevents": "pipobscure/fsevents#7dcdf9fa3f8956610fd6f69f72c67bace2de7138", "recursive-readdir": "0.0.2"}}, "0.9.0": {"name": "chokidar", "version": "0.9.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.9.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "c1ae41561dbdb89dd5fac615453d20b48a946c2f", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.9.0.tgz", "integrity": "sha512-lo5j8BRespGYddDgKnzjblnrh9wzCacnegaIopE2RvoAR0+W20bF1jeRVlhOUHdG/y4RbOcEkoWytfiVbUpk6g==", "signatures": [{"sig": "MEYCIQDpzs8uttDo/yCPYp2zU0qb2hIU7ET8QPGZe+/oOrrf2gIhAL5MGPNEMCH5mJnM2vo6/hktxWqaYWRTZ4VaGByFhqgI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "c1ae41561dbdb89dd5fac615453d20b48a946c2f", "gitHead": "6ae0ec0de336119fba2c1c6bdfa2487fa59cfc4a", "scripts": {"test": "mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.0.0-alpha-5", "description": "A neat wrapper around node.js fs.watch / fs.watchFile.", "directories": {}, "dependencies": {"fsevents": "0.3.0", "recursive-readdir": "0.0.2"}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "rimraf": "~2.2.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}, "optionalDependencies": {"fsevents": "0.3.0", "recursive-readdir": "0.0.2"}}, "0.10.0": {"name": "chokidar", "version": "0.10.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.10.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "0079e9cd2c92f65f527893117ae63cc6fd03292e", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.10.0.tgz", "integrity": "sha512-0DIxXwg5kDmEr5PFfQ7LStYE9HxDc2h3zqd++7qrtXTmXPZADRlUDhGSUuhpVbM0/Uskeb8rV9lq5ghsKywkXg==", "signatures": [{"sig": "MEUCIQDeO9oCvnaSbSMAmFpSCkQj7f+UCr94YQYvLyQDHuTcXAIgB2949Fb/1c8NjSjRC/Xvh3WjSx2n9KjxOLh9hSiU4Vc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "0079e9cd2c92f65f527893117ae63cc6fd03292e", "gitHead": "30a7d9eebd65c2a914969623a874c505ff9537c4", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0"}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "rimraf": "~2.2.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}, "optionalDependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0"}}, "0.10.1": {"name": "chokidar", "version": "0.10.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.10.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "ec2b4e9910c75a2b2e09ff5fdf283029b73af199", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.10.1.tgz", "integrity": "sha512-hoY1bxXuSbHKoj7Usj+Me2rPUaCXQSik/d967IS//7QkVjrp7BiGKZw0h6slr3Q9iCr1LrV1IYtpuuJxfDyiiA==", "signatures": [{"sig": "MEQCIGAbpO/ys3V4KR3TBfbRBnJLGBNuHxM8w3byVH6njncyAiBiFEpKU8KgUnHzYqUntB6q8YbZ6IqY2kTZRDLaiytRTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "ec2b4e9910c75a2b2e09ff5fdf283029b73af199", "gitHead": "88043f82830302e02c618631365f07662ee03d61", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0"}, "devDependencies": {"chai": "~1.4.0", "mocha": "~1.7.3", "sinon": "~1.5.2", "rimraf": "~2.2.2", "sinon-chai": "2.2.0", "coffee-script": "~1.6.0"}, "optionalDependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0"}}, "0.10.2": {"name": "chokidar", "version": "0.10.2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.10.2", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "54b4221d6c8e0c57f2d2e432f8212d64d205b240", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.10.2.tgz", "integrity": "sha512-YoYTjmdG1xhbUuEGCAI/we1IlehPMymu5PbEJhWPBkI7S17Ajn0dzlvfGAYbCxieoD1oKdpCHs5y4KLQ4A2uyA==", "signatures": [{"sig": "MEQCIDni8UTUyCop26te3lRmq2N6vDZGxUEsa7jwydBFiEq3AiBNRhx/wY8OA2CuS99H4UZhHZvbH4llmvMBAGjSzjGjBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "54b4221d6c8e0c57f2d2e432f8212d64d205b240", "gitHead": "ba20ee6779db57f42280bcd263b9cec5e3e9476c", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0"}}, "0.10.3": {"name": "chokidar", "version": "0.10.3", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.10.3", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "5f228db51820140d0f9f712bd2c29f4fbc1bba49", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.10.3.tgz", "integrity": "sha512-b<PERSON><PERSON>bhuRBaXwKI2b9ngLGtyNDa25KxWk2vk81ERdvHUUWMwgPC+TqyBhSRAcT+Cgjt6J3W1UNnqB5AmuE2DuZWQ==", "signatures": [{"sig": "MEUCIQDNa7M9l96BJ+WCGd9t0xGk8mzKPTIWAsrAgW8R6W4regIgMieZ7bxWJdnwIWBKeEBYBgEahguFZxSlB2GRGYRpPCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "5f228db51820140d0f9f712bd2c29f4fbc1bba49", "gitHead": "c97a94c842818b829d1fad0cb88207988c460ac6", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "0.3.0", "readdirp": "~1.1.0"}}, "0.10.4": {"name": "chokidar", "version": "0.10.4", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.10.4", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "f223829f5511b79ac0f888e981a93b241d01c89d", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.10.4.tgz", "integrity": "sha512-lOAU4qAlVco4hRTKinevYW6400zTV4EAp4qrI2vnBZVWaG+H3F73ZrNpUR9q+6Rm/0puZ0T5lniDjTtC/ciiAQ==", "signatures": [{"sig": "MEUCIBOzcTmV7h7IhY5Sy6gYs9UHfmbD7yGrCP64+/4ci/vTAiEAu+SJpiVcSbyaBU2cjhsIVnlfP825qTBFpXIT1XYyrCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "f223829f5511b79ac0f888e981a93b241d01c89d", "gitHead": "93506dd06ab2f67a5a9d527f42ec08569d85d938", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.1.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1", "readdirp": "~1.1.0"}}, "0.10.5": {"name": "chokidar", "version": "0.10.5", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.10.5", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "4a5f343c4562cd2c48fa9e6531c3feebe21eaf79", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.10.5.tgz", "integrity": "sha512-NuUuBpAabKdzrl9ee9eQ9mjSlWHUGvx7UIZcc6wFyOd0Nz4d0DGDsZwm4bt7Tu2/69ds33Jlx/zrwhpT88RgEg==", "signatures": [{"sig": "MEUCIQCfuY89ov/PfM8ewmOqi1WFQB1iYAE3MBvGGtNoP3dOwwIgS2SFKp4h3uR2GZfnS6J7ErbJqcx2uIBuBaRXYG4hJbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "4a5f343c4562cd2c48fa9e6531c3feebe21eaf79", "gitHead": "704d80225175b9eaa1464f4c71deefcaaee57765", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.1.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.10.6": {"name": "chokidar", "version": "0.10.6", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.10.6", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "e911c29d27bb422e61c3d2eaf621faae23c3741d", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.10.6.tgz", "integrity": "sha512-8DsXKR6XJaf1IQ8+u3YH1jV2+qHp5r+J5vUrRO7fefHsPTC/MBzI/mWJwVS0IZWgfdvikoassHImCn2VnpyWyA==", "signatures": [{"sig": "MEYCIQDtyO3T1Oi0xomu7qiJB3XKRYT8by9vebJktI6w76PQKgIhAJFORPqclSVLktEi+BGt/g1900YCUijYH7HvBOg1ziQN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "e911c29d27bb422e61c3d2eaf621faae23c3741d", "gitHead": "3b229c8b1ddd96d985e8b4cda8123511ba2ae9ac", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.1.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.10.7": {"name": "chokidar", "version": "0.10.7", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.10.7", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "50a097e8bc1dd0297bc6b7d8c21aa4b9fdaa068d", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.10.7.tgz", "integrity": "sha512-rD/ZvcgNZXsiTQhp+ofnmqvJbN1ik/eNu0g4a7vIGDD+H9OF1HYkr7EG6AEXOH1BR0V4sLnllMnHjviKILgy3A==", "signatures": [{"sig": "MEUCIQChGn5x8EWTYibChVrI9qyxmsD5ul+6p9qUj3WCEtRV7AIgcaGyuLfI3sON5nLHJ7UbNqlWbxtt+L8EN50akp8JElQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "50a097e8bc1dd0297bc6b7d8c21aa4b9fdaa068d", "gitHead": "b4c76e188f091c7848b6523612628f88e5a9dc0b", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.1.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.10.8": {"name": "chokidar", "version": "0.10.8", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.10.8", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "f19abef62746ec4e252195ff1754df4351ad2ef8", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.10.8.tgz", "integrity": "sha512-JnP5wCPWR3Xdn78n+LHrwipWhVnaE5g38S5EFEG66pBOatLAodco8wLgupioU39yBNRTytebqyJ/NmHDGrK6iA==", "signatures": [{"sig": "MEYCIQDQvzXXaSI6Ks0SUEFOrkfpgu1+WwVnND8WB6cc+iipPwIhAPJo+CnznEgZiSha/x/2khM7yonhiFRNPyz3pFtm0gwM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "f19abef62746ec4e252195ff1754df4351ad2ef8", "gitHead": "34213cb36f4a1bdb6e3aa92e59cc4fbaba0801b7", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.1.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.10.9": {"name": "chokidar", "version": "0.10.9", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.10.9", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "7e53db756aee61a731365c00753a35d2513e4fee", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.10.9.tgz", "integrity": "sha512-q68F/4UvB3CNA2PXfSMXE1jrzSx79Y2ggJmz/3pv39i8Luit8j1P753aIE/I16mSDg+oJbDrWbFhsyDwq1u8rg==", "signatures": [{"sig": "MEUCIQCzBgbHTGZqFrCmaBygjFuAGCTY/wDgGqPHBef5Dnl2MQIgD1oROdiehRRlKW3sNcihbDzwE+ld2g4eYW01FCORB34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "7e53db756aee61a731365c00753a35d2513e4fee", "gitHead": "a7966d00dae54c3572ff4ffdf76cab84e347b979", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.1.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.11.0": {"name": "chokidar", "version": "0.11.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.11.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "9a556c68e426ff4d13841d4bc826819fa2f2f61d", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.11.0.tgz", "integrity": "sha512-G5rfDJPTOMb0TdOLOATBf3+EbXCRA52Ictqm2C6cJ7tZY3+ZmsdZ9q4XsRy9cK5s098FoqFx9t3AZXJlIzYulw==", "signatures": [{"sig": "MEUCIBniEotazxCB88ixohObAHZgjw9o9+rf6JybSltUQCDpAiEAwRGlOvBRmudxLSSfnXhsByNQCPBr2YVsNLWiPd0xz4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "9a556c68e426ff4d13841d4bc826819fa2f2f61d", "gitHead": "5c1f795a9c4c7896ea833106ed82320d3e3a632d", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.1.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.11.1": {"name": "chokidar", "version": "0.11.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.11.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "b00e01717de445783782ef5c48a803e05fed0fc4", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.11.1.tgz", "integrity": "sha512-MSSZgpYMN/MJalJ19zAmSxlxYkfpOTnt7xutjQTuQ3YCf5/PypW3KA6PwpXmi4G3RqB9bnrNJaP59iOjZuet8Q==", "signatures": [{"sig": "MEUCICod2t2Gj16/AK75Ox6PebUADEbKJPQ0TI0l7/XX4EaTAiEAn9r7K7LdC1oXOSNJm2+8pyS6UHTvwiGdd6ymEE/26zQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "b00e01717de445783782ef5c48a803e05fed0fc4", "gitHead": "c79f39cd23ebf89ead09a99c6207b813c26cb67d", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.1.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.12.0": {"name": "chokidar", "version": "0.12.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.12.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "71dbee9c5c6867f6b3cab081ce9f05ef9b8a0532", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.12.0.tgz", "integrity": "sha512-Y9iTb7/Hzv/y874NLyeKp8r4lEKoGFbGrG3jQAOKZiIlI3uW12aXfa/H0YSsja9uDMipVDjRZQFJHBPHTzhUgQ==", "signatures": [{"sig": "MEQCIFbRfEHYpyi6nqCD6pPtUBa0XsRUIy1kzJi+G1NV08nMAiA3C+tUUzyjvHBWzaTcgfWR8smXNsFiH6lmuHH1C9ehNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["lib"], "_shasum": "71dbee9c5c6867f6b3cab081ce9f05ef9b8a0532", "gitHead": "bf9f6e6a04a33df5715e30c2487c54a3f95343c6", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.2.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.12.1": {"name": "chokidar", "version": "0.12.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.12.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "b988914fc44d2869334fbc3b7e72256c37ab6d54", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.12.1.tgz", "integrity": "sha512-JOFOHWHUgU7XLoht6Z0/4VsMEKxNyZNCj7GJpRkgEahacWxcF4sBTN/h2P3idgO1SFkvoF59gRmwJUg801Rfaw==", "signatures": [{"sig": "MEUCIAYENfYX9khjEtc2UVNq7TpxWYogrtatfzc+GoOG++0FAiEA4A8TrJBPZbZ00iY0ZTWJZazb9r9dF0dfCoCd7bwVw+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["lib"], "_shasum": "b988914fc44d2869334fbc3b7e72256c37ab6d54", "gitHead": "abf037886b17c1fef2b32b6038da9979bd193d41", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.2.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.12.2": {"name": "chokidar", "version": "0.12.2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.12.2", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "9ea1719bf2cc41136ea6e8a3dc7d08ecf36c8c12", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.12.2.tgz", "integrity": "sha512-WpS8oMsv8evIbg+mJQztuaAHL3Jlf9Y94apqZ0wj062p4lWkYdOfOQfXMXq0qvcZYfUmFNxrY/CI3rqW43PtOA==", "signatures": [{"sig": "MEUCIQDu10F1W31KMpJmHK/t6FLdPlYkMHKaaVxT9u13ss1g5wIgBz7TF+rOGkvO7z8EAFuFfseY36FMlWARjghCfAA+mNY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["lib"], "_shasum": "9ea1719bf2cc41136ea6e8a3dc7d08ecf36c8c12", "gitHead": "29e485f0080e7cc2a5f6c1d5549131f25bda8179", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.3.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.12.3": {"name": "chokidar", "version": "0.12.3", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.12.3", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "02987da6cae9ebc012370a225a1184ad69940683", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.12.3.tgz", "integrity": "sha512-ZNqGPHSIsk1pHA3EUGftftoUg9C2DpZBOEkHxJp7lUCIhXqDht/8hm4fmcl2JAI0Se+tlRR18vZ53Ld4iLwqig==", "signatures": [{"sig": "MEQCIDlRZzMiK3ZWYoKpO6OKpQ0lsEWFz8CKctSinH7F49YPAiBnpMylfUnzAnlwF1o6hJ3duxyBYSHEDh3brefJIp6cGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["lib"], "_shasum": "02987da6cae9ebc012370a225a1184ad69940683", "gitHead": "b952a4c16161a640926263625e5a800b5b583295", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.3.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.12.4": {"name": "chokidar", "version": "0.12.4", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.12.4", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "572e9299c5f820ebb02d2ba1e5d06a52bea75706", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.12.4.tgz", "integrity": "sha512-b2CvF9QRrjjeg4rYU60jNJ04jWWBln9msii1QSKYjNFoBV6mazIIL9vW2hJKmo4eVhXz0oasm7ew0gcHc6fD6Q==", "signatures": [{"sig": "MEUCIDyxHxSDyNvpogayG1FBicIzv2AR/hlT6R9Z69fgU32bAiEAqRQPg7W0F1FXhgRyZb9yVkIi4Mind2s+3ZckqF4hYuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["lib"], "_shasum": "572e9299c5f820ebb02d2ba1e5d06a52bea75706", "gitHead": "c7a7d9ff03667a0e3d45c90522f386631d5b43ce", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.3.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.12.5": {"name": "chokidar", "version": "0.12.5", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.12.5", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "69ce0c49752dc4d32b5a45023b4b83661f858107", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.12.5.tgz", "integrity": "sha512-T8nlJ080ztlzS+7j5+3L1G9pFi79I4wL4mKbFdTVMNFA6ywM2yKjpEPPiMUw4T+bXGJiTI46hXJVzCDbEpCBTw==", "signatures": [{"sig": "MEUCIBY9ddpFOBuMv92jq6NQh/c5X5d8voO1LsF9syl2GzxIAiEAkDH9gclKXd+IC6ORHFIsLjx7q+TJM8X3w6xM+kASumM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["lib"], "_shasum": "69ce0c49752dc4d32b5a45023b4b83661f858107", "gitHead": "fa94c0f26d6a0f8a7dfe5e9143997d7a3f67b0d4", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.3.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "0.12.6": {"name": "chokidar", "version": "0.12.6", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@0.12.6", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "be204f5b9634e009311256e5d6e8e0e508284d2f", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-0.12.6.tgz", "integrity": "sha512-nMQqT43NJ46Yc75wx9pQ7p/yhzg/5qXHPcgHKUcTLCdcUr1xMUM92SSY4sfGNpJ2zRHQqUsk1hnlP4GW8mm0Aw==", "signatures": [{"sig": "MEYCIQCRYcxICy8a+UlkiOavsn++8yDQ81Usq8B/2IQVy5u5WgIhAJnymVYbhZVO6yyFm2FcXcWlYXtNr0unwJXsr8v50QuK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["lib"], "_shasum": "be204f5b9634e009311256e5d6e8e0e508284d2f", "gitHead": "dec31efd2f30b907bce324ff9fb2e4f1e782f92b", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "deprecated": "Chokidar 2 will break on node v14+. Upgrade to chokidar 3 with 15x less dependencies.", "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"fsevents": "~0.3.1", "readdirp": "~1.3.0", "async-each": "~0.1.5"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "1.0.0-rc1": {"name": "chokidar", "version": "1.0.0-rc1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@1.0.0-rc1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "b1b447f2b388375820d8e766dd918bd109ddeb87", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.0-rc1.tgz", "integrity": "sha512-osvJYJ4Eh1994GDDZAahcrrid0x6w2AW/tWNjV6yRwGCCyoLGsRt9PZlspG4CARy79yDNcCbKBe/hiGADaQ/bg==", "signatures": [{"sig": "MEYCIQCtF3B7R+d/BSPzQlmiqYk1ArRclbgp1sIo4qrimaDqEQIhAMl6FZQqYjTq3ohHNmF+wvXCvmsGgrpe+5dXon2Fuzm8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["lib"], "_shasum": "b1b447f2b388375820d8e766dd918bd109ddeb87", "gitHead": "b40daaca95a26f9df8d1d3b06968d851348b0ed8", "scripts": {"test": "istanbul test _mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"anymatch": "~1.1.0", "fsevents": "~0.3.1", "readdirp": "~1.3.0", "async-each": "~0.1.5", "glob-parent": "~1.0.0"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "istanbul": "~0.3.5", "coveralls": "~2.11.2", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "1.0.0-rc1.1": {"name": "chokidar", "version": "1.0.0-rc1.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@1.0.0-rc1.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "e4ec078b2a10778963f94c00e5dca1696e6842cc", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.0-rc1.1.tgz", "integrity": "sha512-TFt92e2iauhdo/gDRqAR6gqyuHhGQe2MruYo/s6kW7GOOoA0MQ0oc+xg6pEWgM+1H6xobcm0j3uESwoQbML19w==", "signatures": [{"sig": "MEYCIQCvtEYsu3rexFDsUPx/QMlHVoAI+qtoZ4QWjASmI6tu8AIhALiNcn4j8R6vNo3GhIPlivTPmD9FmeDD53GqnAI21K45", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["lib/"], "_shasum": "e4ec078b2a10778963f94c00e5dca1696e6842cc", "gitHead": "b40daaca95a26f9df8d1d3b06968d851348b0ed8", "scripts": {"test": "istanbul test _mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"anymatch": "~1.1.0", "fsevents": "~0.3.1", "readdirp": "~1.3.0", "async-each": "~0.1.5", "glob-parent": "~1.0.0"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "istanbul": "~0.3.5", "coveralls": "~2.11.2", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "1.0.0-rc2": {"name": "chokidar", "version": "1.0.0-rc2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@1.0.0-rc2", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "7adc3966f8b97492a95b7d8b98ba2c21cbdaa7a1", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.0-rc2.tgz", "integrity": "sha512-wa1GKFymb2ND+HnWsCK58/tpCiYXO0FLq6N9vYvi9DvafVn/++F38sdWmMDyHqh8gGUQS5weENvR9e/HS3bRnA==", "signatures": [{"sig": "MEQCIGfyKNjCRVpL5tbawwMczi0RvWOF68l1c4Oq4adk11DOAiB9H5ITuwrL9h1Zyb5oN0B72GOLAnXdrdC2vqC9hiCO4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["lib/"], "_shasum": "7adc3966f8b97492a95b7d8b98ba2c21cbdaa7a1", "gitHead": "972e7b75af0e91494eadcd115104098d979d08b9", "scripts": {"test": "istanbul test _mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"anymatch": "~1.1.0", "fsevents": "~0.3.1", "readdirp": "~1.3.0", "async-each": "~0.1.5", "glob-parent": "~1.0.0"}, "devDependencies": {"chai": "~1.9.2", "mocha": "~2.0.0", "sinon": "~1.10.3", "istanbul": "~0.3.5", "coveralls": "~2.11.2", "sinon-chai": "~2.6.0"}, "optionalDependencies": {"fsevents": "~0.3.1"}}, "1.0.0-rc3": {"name": "chokidar", "version": "1.0.0-rc3", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@1.0.0-rc3", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "f95d5e60c7d66eb53136c8999c47e9d4f37118f5", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.0-rc3.tgz", "integrity": "sha512-oj+7cyPncU3XJzgVxWOG9cMJscy0i79yA/QCghVrMB+w+lK+6xdQDGOp85FuNidS6eOdea8KKO9NcVOBqGwLIA==", "signatures": [{"sig": "MEQCID2JrUSzulWu9YNtGwT7Wgzkx1YrtQznucddZbmAtmgEAiApX5xyWneMooBfe25h51GUOCB5fVlIBOGCsNzfIougsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["lib/"], "_shasum": "f95d5e60c7d66eb53136c8999c47e9d4f37118f5", "gitHead": "222a71c92a63c1cd02fcdb95c5a3c3b875686d84", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "1.0.4", "dependencies": {"anymatch": "^1.1.0", "fsevents": "^0.3.1", "readdirp": "^1.3.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0"}, "devDependencies": {"chai": "^1.9.2", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.5", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^0.3.1"}}, "1.0.0-rc4": {"name": "chokidar", "version": "1.0.0-rc4", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "choki<PERSON>@1.0.0-rc4", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "312378ae793e6320591f230f0f69c0de93d02b6e", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.0-rc4.tgz", "integrity": "sha512-pVJXqZyS5bNdRVyZqpgm8ZWQyrJHyVysnt8BGMxdTa7gA1bdx807wsjne30EmknsrEF/lvnnlHk8OinnC3E+ZA==", "signatures": [{"sig": "MEYCIQCkoeO/jsS/HqVeKl0v5tUWnrcVMSB+CGZ2dl5i4JQGAgIhAPC9zHVxN9sdt8SwbxNjVK1X6G6SmU5znFLhIP6zXdcg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib", "_from": ".", "files": ["lib/"], "_shasum": "312378ae793e6320591f230f0f69c0de93d02b6e", "gitHead": "e80e0825a3f31a578039ed0ad610c4cac4ad35eb", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "dependencies": {"anymatch": "^1.1.0", "fsevents": "^0.3.1", "readdirp": "^1.3.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0"}, "devDependencies": {"chai": "^1.9.2", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.5", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^0.3.1"}}, "1.0.0-rc5": {"name": "chokidar", "version": "1.0.0-rc5", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "choki<PERSON>@1.0.0-rc5", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "45cb28ae7478b981d739b35f1e18d687aeb55802", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.0-rc5.tgz", "integrity": "sha512-JRTdNSzGWE/LTY/q8QPAZOg23Q7DFx+b0qCvn+xtVqKdKug+I9yY0GNgEj7J5ZN3rj21Ef3ou0AQKFl6/9iDHw==", "signatures": [{"sig": "MEUCIQCr6D0gC1AkPL1PVDm5lSV7lec0KzpwuRfrVhD9Ia++eAIgRvbwFx+TrdtVUiYTvQDREb6QOVF9x5LMuUgYR3xZQdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "45cb28ae7478b981d739b35f1e18d687aeb55802", "gitHead": "63fa12753e49fa799c5ddc63b61229008d31086e", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.7.3", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"arrify": "^1.0.0", "is-glob": "^1.1.3", "anymatch": "^1.1.0", "fsevents": "^0.3.1", "readdirp": "^1.3.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0"}, "devDependencies": {"chai": "^1.9.2", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.5", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^0.3.1"}}, "1.0.0": {"name": "chokidar", "version": "1.0.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@1.0.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "4af904c033eda714142b8ac371498b939923f8c9", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.0.tgz", "integrity": "sha512-o4b924x32FEt3XGlA+tf89c2VrYqEot7AQQucAPGZq6GHkO/MBEpDAcOq4p4lmFPYP9V2fLFyP4nS+WUsp8eFg==", "signatures": [{"sig": "MEUCIQCSP0KnQHGrEGmpo9ZE0GhJ49WJC32XlzoH9D+p3y6v9wIgRMNaxSmFrz9FKgpK52+Kf6tXqOkZLcWQ87R18Mot6vU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "4af904c033eda714142b8ac371498b939923f8c9", "gitHead": "f617546aa33f2ceafb51359b65a7fddd0b882b2d", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"arrify": "^1.0.0", "is-glob": "^1.1.3", "anymatch": "^1.1.0", "fsevents": "^0.3.1", "readdirp": "^1.3.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0"}, "devDependencies": {"chai": "^1.9.2", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.5", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^0.3.1"}}, "1.0.1": {"name": "chokidar", "version": "1.0.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "_id": "chokidar@1.0.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "b19e476a071ac0c7a01279cdc936e0d31c6ee06a", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.1.tgz", "integrity": "sha512-3oejTYOC9bXBLTfepjySaTtENhSCLzSqz8e5zxzKPtiHOOxtNKwnmLY2fLHzzgsy70al1MYvJWXSv3qaQvM+rw==", "signatures": [{"sig": "MEQCIBg8fni//RQ48ZS8Y+W/b0T8k72nVjgJcAHFfFvqPM03AiBG+NogOktqpecQ7eZpFCHOEb7TJbkh56yDaK5NR2lJVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "b19e476a071ac0c7a01279cdc936e0d31c6ee06a", "gitHead": "beb9db68404a93cac844bbd2f5b5c4e25559d1a5", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "licenses": [{"url": "http://github.com/paulmillr/chokidar/raw/master/README.md", "type": "MIT"}], "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.7.3", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"arrify": "^1.0.0", "is-glob": "^1.1.3", "anymatch": "^1.1.0", "fsevents": "^0.3.1", "readdirp": "^1.3.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0"}, "devDependencies": {"chai": "^1.9.2", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.5", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^0.3.1"}}, "1.0.2": {"name": "chokidar", "version": "1.0.2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.0.2", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "c5d4f226ccd0044a16278498eb009c0715ce48c1", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.2.tgz", "integrity": "sha512-PYSwZWlqEPhNDwqvCfkj/O+te0FIIIgQEgHtwA9lT68EhjquOgMsrie/tygnlnD8QWFKaLf5JcgKeOKvxGgfTw==", "signatures": [{"sig": "MEQCIBejpwhIriuWHotXdcCrhEsCJLxWhxOAtepk8C+4NvFXAiBW9lHYsJdvTnoxH5gq7PlQ7c0ADXy7SEM+n/TxWQCTuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "c5d4f226ccd0044a16278498eb009c0715ce48c1", "gitHead": "33320c3e40a199a87c61951a145d7c1011f3a13f", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"arrify": "^1.0.0", "is-glob": "^1.1.3", "anymatch": "^1.1.0", "fsevents": "^0.3.1", "readdirp": "^1.3.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^1.9.2", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.5", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^0.3.1"}}, "1.0.3": {"name": "chokidar", "version": "1.0.3", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.0.3", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "ba63580caeb89bbdf869eab51bbca4f3ca441be8", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.3.tgz", "integrity": "sha512-qb19c7VYGByaxL4ApVnbRle7g5bPvg8MrgKo0DhVO60NLgmYllQFP77HX/Fech74ygCDaIWR0BI9wEliCLrurQ==", "signatures": [{"sig": "MEYCIQDDoCd0Wp6XjmxNitm1dqd25hJlbKNvC/oGopvOinq5dAIhAJz1UXtIWzxmLFFwC61Xs4JRQ2TBdS7MmrVnn0u1io45", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "ba63580caeb89bbdf869eab51bbca4f3ca441be8", "gitHead": "ba90462886d0fbb1927f70fe2e7a25a4249b9994", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "2.0.0", "dependencies": {"arrify": "^1.0.0", "is-glob": "^1.1.3", "anymatch": "^1.1.0", "fsevents": "^0.3.1", "readdirp": "^1.3.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^1.9.2", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.5", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^0.3.1"}}, "1.0.4": {"name": "chokidar", "version": "1.0.4", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.0.4", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "bd9fbb1ef0cccc60e2da10c9aeafad6f2c72043f", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.4.tgz", "integrity": "sha512-45auOKIEL7StIyNKtQi/07E2HZo86LgpcOl30SNabmdbbYnZV34zIejMuhJhAIg2LKPOVM4gUlLMNmCfJT9Gfw==", "signatures": [{"sig": "MEUCIHxHTnSoTsHjjnkeFhvnyDbdac3AAVeIntimBRauHg8RAiEAv1NfXRDhW05i9wVZxU+LnZs/N4mxboz31+DcjCfPn6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "bd9fbb1ef0cccc60e2da10c9aeafad6f2c72043f", "gitHead": "769307823d2ec266e3679c24a080116a64680ab9", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "2.0.0", "dependencies": {"arrify": "^1.0.0", "is-glob": "^1.1.3", "anymatch": "^1.1.0", "fsevents": "^0.3.1", "readdirp": "^1.3.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^1.9.2", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.5", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^0.3.1"}}, "1.0.5": {"name": "chokidar", "version": "1.0.5", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.0.5", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "f29278a36e174365da56ccad488ecacce4893494", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.5.tgz", "integrity": "sha512-bsPSV7fQoq/fdSas3Ld1nQAkezda4BvUcUM+tV10bw55dBYrfR2WMLSY/ZZqbUcykl6qvNbEmHGf7FTq4AWJ1g==", "signatures": [{"sig": "MEYCIQC+gKQlxAikJRbmpDR7y57yy1FUFdKg5apCbSnUxr1G2QIhAKDfnI4wdirtQ7druOJ4o86naUIePu8UPMuidtzZkbw3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "f29278a36e174365da56ccad488ecacce4893494", "gitHead": "c9b6054e0951bffa3df140d57199d8960b7a1376", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "2.0.0", "dependencies": {"arrify": "^1.0.0", "is-glob": "^1.1.3", "anymatch": "^1.1.0", "fsevents": "^0.3.1", "readdirp": "^1.3.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^1.9.2", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.5", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^0.3.1"}}, "1.0.6": {"name": "chokidar", "version": "1.0.6", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.0.6", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "0a1c0bce1e24993afc105a5b81ea26dda01e23af", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.0.6.tgz", "integrity": "sha512-Fz5EFUY/h6X5ZUsUl2wtokjAax21dsj1RrmDidVWts8vvFaroEHSFmBK87Iqah56XDIinsh/n82S9r5kZmy4DA==", "signatures": [{"sig": "MEUCIHlrCsJLdYJoW0r1huZnONjXM4uQwCmg9bwv85EWnDGsAiEA9RhcjFMM4gqncq5DefFITpKok1O8VKe0/oYlEFWiNho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "0a1c0bce1e24993afc105a5b81ea26dda01e23af", "gitHead": "bc38ab360f1bf1020f074796e2be014caf785321", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"arrify": "^1.0.0", "is-glob": "^1.1.3", "anymatch": "^1.1.0", "fsevents": "^0.3.8", "readdirp": "^1.3.0", "async-each": "^0.1.5", "glob-parent": "^1.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^1.9.2", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.5", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^0.3.8"}}, "1.1.0": {"name": "chokidar", "version": "1.1.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.1.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "15aa9169424828895c6c8aee4eee8d8156820b9f", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.1.0.tgz", "integrity": "sha512-mZ5ef/iDew7YO4oaiN5jDwW1Ttwx2uTMXb528JF7Wl+Titpm/imo7wY+Mxt+0n527h5CUzXlNCJv1Hdax/T7fg==", "signatures": [{"sig": "MEQCIEpQIrY2Ui2HmUrqQjGWOfAoDKkOqhDPeoxlbUW59+GDAiBgGA6xiFfZKJ96dqde/WtaOvAPW5BRSip+7Bm8fy9WGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "15aa9169424828895c6c8aee4eee8d8156820b9f", "gitHead": "07d9e78c67cc918c5903cc49b610f7a6b2ff9d52", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"arrify": "^1.0.0", "is-glob": "^2.0.0", "anymatch": "^1.1.0", "fsevents": "^1.0.0", "readdirp": "^2.0.0", "async-each": "^0.1.5", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}}, "1.2.0": {"name": "chokidar", "version": "1.2.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.2.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "d7cc02d05e94092ddfacad488ebebe588ff2ff30", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.2.0.tgz", "integrity": "sha512-7O2ZUP1+4g8rJQre2LkG0hJCfY8aLuutetFoitwj0sDzzc6lL2D4nyah6JmIu+PmG4gcxUIhWxDb68mdIUoLFA==", "signatures": [{"sig": "MEYCIQDuCqjA+46QnGI1QP7NNqHirjfMVhYcZ+xB06KRkO0i0QIhAJpib5F/xizw7selnW5ZRaQIJBNvGpnA7rLCX7jqAgxX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "d7cc02d05e94092ddfacad488ebebe588ff2ff30", "gitHead": "b0e122a984f731c0829973242d05b63e7c79176b", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"arrify": "^1.0.0", "is-glob": "^2.0.0", "anymatch": "^1.1.0", "fsevents": "^1.0.0", "readdirp": "^2.0.0", "async-each": "^0.1.5", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "lodash.flatten": "^3.0.2", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}}, "1.3.0": {"name": "chokidar", "version": "1.3.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.3.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "a7c2af0f4234b5d83e3491e403817a88d517e4ef", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.3.0.tgz", "integrity": "sha512-6IzBBaG0l2O+z3AOfMzvAKLB6bEXW+K7oB9JXJ8De/ibiyqP/TCh8PbTuoUSL2agbdZ5/DeMoaEG+MEV3Euf0Q==", "signatures": [{"sig": "MEUCIQDCr+ibI45/kzUkN7gSp1sRF4kU3MUvy3h1t2DVTCyydQIgZhRZ+kOPk8s2Wx1EdAuVMXjYfonHQDS102BpQnUBFC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "a7c2af0f4234b5d83e3491e403817a88d517e4ef", "gitHead": "a25a52a1cbbed8fb612bac219e5181e32a53b2f7", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "4.1.1", "dependencies": {"is-glob": "^2.0.0", "anymatch": "^1.3.0", "fsevents": "^1.0.0", "readdirp": "^2.0.0", "async-each": "^0.1.6", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.0.0", "sinon": "^1.10.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}}, "1.4.0": {"name": "chokidar", "version": "1.4.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.4.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "bb6fc855413c2e6ca5f54250103a692f2f64f51e", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.4.0.tgz", "integrity": "sha512-ZFvBXIe00z6UHTyZTvtLb7UKbjWYqVC3ZWixSIHwJQkH5E6TenKoVklsnhR2TWBJVruyXN1ODgioRPRdTxlwhw==", "signatures": [{"sig": "MEUCID3t20B9JxVsSjL1tD04bh8qEqdFyMsBjxK+S3ROgPCEAiEA11B/e2IqMmg2JesaryHlPg4YFo/u38CTYtZBhr3WDD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "bb6fc855413c2e6ca5f54250103a692f2f64f51e", "gitHead": "899c2438a7c3a24657f969e266ca3092e85c0512", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"is-glob": "^2.0.0", "anymatch": "^1.3.0", "fsevents": "^1.0.0", "readdirp": "^2.0.0", "async-each": "^0.1.6", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}}, "1.4.1": {"name": "chokidar", "version": "1.4.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.4.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "df1d906769701a0f3df492c37dcc3cb35e6450e4", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.4.1.tgz", "integrity": "sha512-5ltk/EAsPN3EJCMtaKsGC29j0mUUtCOjUajWsnPfQNTspEscbLCTVSVV/6AvE1F7kjjUO+SlfE9KP0XIxJkP2w==", "signatures": [{"sig": "MEQCIFspmWgErLOpIzFYbyL/LZVDft6jNH9tqBQyW3dVWERPAiBiQWOQTrx3PYJ7Au2ZDdWYaaELOpUzuZHjTYoJUAARgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "df1d906769701a0f3df492c37dcc3cb35e6450e4", "gitHead": "ece3a67e6ff13bd88584ed3ba2b43198c46651a1", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "5.1.1", "dependencies": {"is-glob": "^2.0.0", "anymatch": "^1.3.0", "fsevents": "^1.0.0", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^0.1.6", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}}, "1.4.2": {"name": "chokidar", "version": "1.4.2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.4.2", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "3eaea6c2898fa7208184a453d4889a9addf567d2", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.4.2.tgz", "integrity": "sha512-OzygdyPQjlOargfc6HJvFqkUq1S5mOiuDrGSLPLeKWQiOG/IVP1uWwLuqN9iSeozSdMxcIoEVdABaSn+y9U2lA==", "signatures": [{"sig": "MEUCIQC2IwlIKivdAi1r7iIrun26obvRFOxbSDyuikRH1mXfLQIgFoU3MDVLA9PTdphpEwLtKZHfRsiHRuOOhuz+RKvE/RY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "3eaea6c2898fa7208184a453d4889a9addf567d2", "gitHead": "1df57f2631849db749ce56c4125e050937c82106", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "5.1.1", "dependencies": {"is-glob": "^2.0.0", "anymatch": "^1.3.0", "fsevents": "^1.0.0", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^0.1.6", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}}, "1.4.3": {"name": "chokidar", "version": "1.4.3", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.4.3", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "5fe733a4d9acaea51b26454b7e59559163d0dbb2", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.4.3.tgz", "integrity": "sha512-Ex9kw8n2e5+rJUMkAAF2U4ZHn75kGltEgYPCG1jeMiSkec7XbPOtmBF1E66V+uzRcfN+qKTqbZ6RomuFIZrhCA==", "signatures": [{"sig": "MEQCIBHlO6jm7/QRM0IfVt9uOs+XgL/uafweS2jj+LWsI+QWAiAyovUUm0UuwNm+cSZ5Dxn8UMzCauI9dtigozM5AKkGLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "5fe733a4d9acaea51b26454b7e59559163d0dbb2", "gitHead": "798d00221fbe11dcdc2c65ea7754fd4399d81fed", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"is-glob": "^2.0.0", "anymatch": "^1.3.0", "fsevents": "^1.0.0", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar-1.4.3.tgz_1456536294331_0.16991215758025646", "host": "packages-9-west.internal.npmjs.com"}}, "1.5.0": {"name": "chokidar", "version": "1.5.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.5.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "7a5f1a72e6ee3e1daffdae74832e8eb28ee2f19a", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.5.0.tgz", "integrity": "sha512-pq65J0fZXsOA2WiZPTIsfliXtsfJHKQYUh6/9brkaV7CORzlHCVRXU8aTBrHJdiWDZGWsxjzK8Edbi1WQfDhyA==", "signatures": [{"sig": "MEUCIQCW0+RdFxAUH5AggseB1fUwE8H/OW9yOUISO68Rf7Y/SwIgdwH9bvycQyM63HXtM/lAnO3Edw5c09XNqKmjGJszNz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "7a5f1a72e6ee3e1daffdae74832e8eb28ee2f19a", "gitHead": "3b6f870bee48f33210af15ef4bba128a668934c6", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "6.1.0", "dependencies": {"is-glob": "^2.0.0", "anymatch": "^1.3.0", "fsevents": "^1.0.0", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar-1.5.0.tgz_1462914212741_0.31828335183672607", "host": "packages-12-west.internal.npmjs.com"}}, "1.5.1": {"name": "chokidar", "version": "1.5.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.5.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "43115fcf2d8fb74f06b630aeeccd06715a146dd1", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.5.1.tgz", "integrity": "sha512-4+UfpWJOkn2JZEY6UXL40BrFm8mNr834P/JLOgMk/DWGCGruL42TDmsBF5kFyZdPER2amGEGMhL7hEgIpWs4lA==", "signatures": [{"sig": "MEUCIQCrdPZPGaGQnUOaUV7QgBbvNwAQHTfdenvTRBOy+vC/HgIgZZYda8K3oQqKghG0otxzIgI/b2nII2niEk5iIgHnvwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "43115fcf2d8fb74f06b630aeeccd06715a146dd1", "gitHead": "638a52986e398fa856429f195994d2a6b5b74b9d", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"is-glob": "^2.0.0", "anymatch": "^1.3.0", "fsevents": "^1.0.0", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar-1.5.1.tgz_1463779048839_0.31024676584638655", "host": "packages-12-west.internal.npmjs.com"}}, "1.5.2": {"name": "chokidar", "version": "1.5.2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.5.2", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "293e728640cc93dd8277424334b3c6d4ad3a348a", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.5.2.tgz", "integrity": "sha512-Hc3T2qGwrnnZORa+Gmnoa5yGkr4cqYURAYBC/RXxPBAeO12C7/stcWXhjvxSnE3gTJMZvSOimFFarZUdd08NfA==", "signatures": [{"sig": "MEQCIHBZWJAU4Ax08AVyhBLCms/1b3VAqGZyAG3A1WrA2aeXAiBqTv2RRtW7eabsNBXU8QG4F2ooh66cthMrSUEoaHi/8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "293e728640cc93dd8277424334b3c6d4ad3a348a", "gitHead": "15c642702060735f5157ee7a23fafb84b50b9a23", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "6.1.0", "dependencies": {"is-glob": "^2.0.0", "anymatch": "^1.3.0", "fsevents": "^1.0.0", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar-1.5.2.tgz_1465334533337_0.7033559519331902", "host": "packages-12-west.internal.npmjs.com"}}, "1.6.0": {"name": "chokidar", "version": "1.6.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.6.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "90c32ad4802901d7713de532dc284e96a63ad058", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.6.0.tgz", "integrity": "sha512-gX708zvyHflAXYD6Nnp4oa3crBOzM4vidTnkBslaJLYRpFKF/36MpPGWT7KkWfiGsq85wJ5AdhixiQgKOI/9Fw==", "signatures": [{"sig": "MEUCIQDCQdTqdp5btZd3a+srap+hz6sBy/hRNFQ82FBJ6pDnKAIgD2qRY3+JVePtl8MhSWebLPVio4kk2PFQOb4E+VkPG3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "90c32ad4802901d7713de532dc284e96a63ad058", "gitHead": "a5c0b127db9d46617b7a3d82cc3fffc08d2ef164", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "4.3.0", "dependencies": {"is-glob": "^2.0.0", "anymatch": "^1.3.0", "fsevents": "^1.0.0", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar-1.6.0.tgz_1466606619436_0.9914595072623342", "host": "packages-16-east.internal.npmjs.com"}}, "1.6.1": {"name": "chokidar", "version": "1.6.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.6.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "2f4447ab5e96e50fb3d789fd90d4c72e0e4c70c2", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.6.1.tgz", "integrity": "sha512-/6SIsjBGK5mzf1i1L8ccsH0jZuzWvMump0iJ6LD3jYxhwiLjvJ+5GrpJNxay9MGRvTAoYmzLU/z19wyxEjfv1w==", "signatures": [{"sig": "MEUCIBC+Tu6hIMHqDsIj6AhwoHFdxQY7W6eM90lJzOS+1iujAiEA3bsXFU/umOLtHCwx38QUujsEsgDSV4v2Fj4V/NoFgcM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "2f4447ab5e96e50fb3d789fd90d4c72e0e4c70c2", "gitHead": "c08145c5368fac6441773e621da9db215cc0d3b2", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "6.6.0", "dependencies": {"is-glob": "^2.0.0", "anymatch": "^1.3.0", "fsevents": "^1.0.0", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^2.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar-1.6.1.tgz_1476470196311_0.5785318606067449", "host": "packages-12-west.internal.npmjs.com"}}, "1.7.0": {"name": "chokidar", "version": "1.7.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@1.7.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "798e689778151c8076b4b360e5edd28cda2bb468", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-1.7.0.tgz", "integrity": "sha512-mk8fAWcRUOxY7btlLtitj3A45jOwSAxH4tOFOoEGbVsl6cL6pPMWUy7dwZ/canfj3QEdP6FHSnf/l1c6/WkzVg==", "signatures": [{"sig": "MEUCIQCbShiBDWNsu5AQ0REe4zyqva27y/m3sQScNx5T19kGGwIgCZTtIG/4YKkK3bxc3UNTKf8ytfeQjGP46Z9gLatJ2Hg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "798e689778151c8076b4b360e5edd28cda2bb468", "gitHead": "3b1071a6dd82397842f4f7dc63b72c703bd06275", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "4.6.2", "dependencies": {"is-glob": "^2.0.0", "anymatch": "^1.3.0", "fsevents": "^1.0.0", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^1.0.0", "glob-parent": "^2.0.0", "is-binary-path": "^1.0.0", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^3.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar-1.7.0.tgz_1494269155265_0.519245742354542", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "chokidar", "version": "2.0.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.0.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "6686313c541d3274b2a5c01233342037948c911b", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.0.0.tgz", "integrity": "sha512-OgXCNv2U6TnG04D3tth0gsvdbV4zdbxFG3sYUqcoQMoEFVd1j1pZR6TZ8iknC45o9IJ6PeQI/J6wT/+cHcniAw==", "signatures": [{"sig": "MEQCIFwnMvynr/J12ssaQ59LNttpwDcndmLUn+x/Pcv5owreAiBYLeOiP2ZGln5PK3snohl+JmfBlXa1Sr6EeKHcx+R9Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "gitHead": "3409db854565caeb17f87d1e5492a507c2a9103e", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "5.0.1", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "6.10.2", "dependencies": {"braces": "^2.3.0", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.0.0", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^1.0.0", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^2.1.1", "path-is-absolute": "^1.0.0"}, "devDependencies": {"chai": "^3.2.0", "mocha": "^3.0.0", "sinon": "^1.10.3", "upath": "1.0.0", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar-2.0.0.tgz_1514586868968_0.5979906069114804", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "chokidar", "version": "2.0.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.0.1", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "6e67e9998fe10e8f651e975ca62460456ff8e297", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.0.1.tgz", "fileCount": 6, "integrity": "sha512-rv5iP8ENhpqvDWr677rAXcB+SMoPQ1urd4ch79+PhM4lQwbATdJUQK69t0lJIKNB+VXpqxt5V1gvqs59XEPKnw==", "signatures": [{"sig": "MEUCIEAs5S41xDm8sqwli8r5rjLlZgsFXPrk/RiCR/8oZy1oAiEAu7OAWfbBXfFiMpyXWuqP9JgBnv8a5tA/8QJVOPZ4BHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78908}, "files": ["index.js", "lib/"], "gitHead": "560cbb978980633833e65ce00ef7ec4b1be80c0f", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "5.0.1", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "6.10.2", "dependencies": {"upath": "1.0.0", "braces": "^2.3.0", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.0.0", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^1.0.0", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^2.1.1", "path-is-absolute": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.2.0", "mocha": "^3.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_2.0.1_1518102659398_0.20342058659857254", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "chokidar", "version": "2.0.2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.0.2", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "4dc65139eeb2714977735b6a35d06e97b494dfd7", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.0.2.tgz", "fileCount": 6, "integrity": "sha512-l32Hw3wqB0L2kGVmSbK/a+xXLDrUEsc84pSgMkmwygHvD7ubRsP/vxxHa5BtB6oix1XLLVCHyYMsckRXxThmZw==", "signatures": [{"sig": "MEUCIQC+MSLpiXSVnz6HP6pMHIlL7oAUlQvQ1zdfOazRQP8VzAIgMweBAoNpi/AkGTdwkX0j1FmVHahQe7DzIzSq9BAQyjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78998}, "files": ["index.js", "lib/"], "gitHead": "4813aec1afda08453355fdf18727931857eb48a5", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "5.0.1", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "6.10.2", "dependencies": {"upath": "^1.0.0", "braces": "^2.3.0", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.0.0", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^1.0.0", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^2.1.1", "path-is-absolute": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.2.0", "mocha": "^3.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_2.0.2_1518626967502_0.605467756614871", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "chokidar", "version": "2.0.3", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.0.3", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "dcbd4f6cbb2a55b4799ba8a840ac527e5f4b1176", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.0.3.tgz", "fileCount": 6, "integrity": "sha512-zW8iXYZtXMx4kux/nuZVXjkLP+CyIK5Al5FHnj1OgTKGZfp4Oy6/ymtMSKFv3GD8DviEmUPmJg9eFdJ/JzudMg==", "signatures": [{"sig": "MEUCIQDX0ATEzIrKH1nZISt3hTnJEUu6INK49Ji1k95Q1Czr8wIgCWFj0bNuBEv6k1u2AMRezLmYpORcjAAoYH7qk/fiVXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79193}, "files": ["index.js", "lib/"], "gitHead": "925b534d63743c941b98bbd1855e12272ce71720", "scripts": {"test": "istanbul test node_modules/mocha/bin/_mocha", "ci-test": "istanbul cover _mocha", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"upath": "^1.0.0", "braces": "^2.3.0", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.1.2", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^1.0.0", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^2.1.1", "path-is-absolute": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.2.0", "mocha": "^3.0.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "istanbul": "^0.3.20", "coveralls": "^2.11.2", "sinon-chai": "^2.6.0", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_2.0.3_1521779380897_0.4438035536428022", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "chokidar", "version": "2.0.4", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.0.4", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "356ff4e2b0e8e43e322d18a372460bbcf3accd26", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.0.4.tgz", "fileCount": 6, "integrity": "sha512-z9n7yt9rOvIJrMhvDtDictKrkFHeihkNl6uWMmZlmL6tJtX9Cs+87oK+teBx+JIgzvbX3yZHT3eF8vpbDxHJXQ==", "signatures": [{"sig": "MEUCICxT0LOyHY2uCHdH/uUo8hpVbjmgzYl5R9XfMgsH9JL1AiEAyuPJejAx9I6gON75TcuvvQQ8ec7W/nU2KdV+SOZQ8O4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbJ+LzCRA9TVsSAnZWagAAQg4P/3Wh32ZSg5hhzlhPLTW2\n6p3YDZlbshBGJJOcLW9JVZbG7Vnhs2UtPe2ypganaITgMIA86vpBI38T6v/9\ntmZaq95orTzRAPj8Au5dBpw6auVOcH3mXqMq56kaZdhhI7dHtoX/EXPa8HvG\ndCi7iuM0uP5X2eEBfWw+Hscd2+EuIvYREH1jIZn3ccQOjSZ+ZyuOmA3zADqp\nJ3xDCp47Io0d9lhTWnuK4rDQqQLEr5wQBtRPtMliqba8Ix5F88XFDKRmZMQm\n0qK9dsMFLD3J6T7+pp0HqDKVvySxr0fY1yD0Ne5w5trkX29Md1sYLtk2c3U1\nrUsJW01LkP6A6qTrWQ2ZKc6YwO4cPJo1cd5KqC2q+UGeHZKiISY6G75ISxPD\n9L9WhaqEeaDSzInTwFIpuO9xj47AP6j90ts91UDH0HHinOzsuXIbKwW+HcEd\n8NjfWqBJAdrJP0Q4S9GN5EeHzigWZOzh42aUEsfcIUZG7Si4EwBzDbbg7rXP\nCPkkLgnY71p9yL4nwY9XhMgJxXXvxKtcmvSLxU0Ve/GbfuTmGK+4lytxJPXE\nB7w+ajrrUJgTQ/GUvwj+RK6fs2XIoQjtUg0Fz0y8CkuhTKCokcLweuSMebH2\nYL0UFhBPQaImybvbtRvSpJFPLydQcVLEmlpdoz/2eWlzqBuK3zgMqSVWPrN3\nK/YY\r\n=1NIh\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "lib/"], "gitHead": "3ad1ae8db07e988fcb1f7f74687363f3022e5c57", "scripts": {"test": "nyc mocha --exit", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"upath": "^1.0.5", "braces": "^2.3.0", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.2.2", "inherits": "^2.0.1", "readdirp": "^2.0.0", "async-each": "^1.0.0", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^2.1.1", "lodash.debounce": "^4.0.8", "path-is-absolute": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^3.2.0", "mocha": "^5.2.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "coveralls": "^3.0.1", "sinon-chai": "^2.6.0", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_2.0.4_1529340658080_0.6771647838611792", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "chokidar", "version": "2.1.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "http://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.1.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "http://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "5fcb70d0b28ebe0867eb0f09d5f6a08f29a1efa0", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.1.0.tgz", "fileCount": 6, "integrity": "sha512-5t6G2SH8eO6lCvYOoUpaRnF5Qfd//gd7qJAkwRUw9qlGVkiQ13uwQngqbWWaurOsaAm9+kUGbITADxt6H0XFNQ==", "signatures": [{"sig": "MEUCIFopCawKAP/kc1RMDPYQUhqaYz01OgpKxtTHeRWjzErwAiEA5Sw8fNqP4sYdIw22TWSFChZQlMo5kn44CKRVLFXt4C0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWeNeCRA9TVsSAnZWagAAwsAQAIRvkMt/vuA4K0qNUEOS\nDVO5xOApAmWSuhwyRphF6rTol7mrA1vVtkN/bJtjsTWE5d80GggehpMtytCF\nhk3jpDhcwV/DBX6ktDnPTfPfmmVkIX+/A2zmbVta94913rUm6lDDTPiPp3b1\naTppoko7kb39q9SVu+zuvPdjjG6512rpTuYeq+lB6cdU2kQn0B3/vbH2/2fl\n+N8MzZqCq0tkZmJub+kjNgVcMN3k+EvpL/RyY0EzViAhutmvphzSbq9oVHDN\nUkg6Pxux39m93+rkfQGD<PERSON>eukgRISekIWe4w4Fm4SWyObkn4G1VZ6eFDus0oj\nkgzRUUFzasZafuT6zmZJz9a6Rf1fxNCe93l/YhMn4qtnySsHJVsFRyf52MWl\nD7gymjd/bVQILKOg+zia73Lv2LmSNNA079KYAgnfVhITY2SGwhWhP9Vmezl0\naNsqILhqMkm47Uqd2p96Re5NL7fwsfoEY16NApdnsEJ8lE6aaU5MmrG0hEUG\nt5IeUsuVCkf/R6XbjtSLijCQ9GBqi9Y/WqVc8RMLM7gjsB7hbYYVTqybjDEM\ngh0pUEP9RQ38Kb9QmmUTXHDwtVr9Sf6A8A8iAVAGmUXbKygYrQNOeiPpF2G7\nhIuEcnSSP2Cg217lY1erFdgdlnMPEPntWGgZttuwGvXbaBC2B6r/ioUNN26u\nuUOA\r\n=r3a5\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "0f25353a4d416262e0ba7062cb28598d74bb82d7", "scripts": {"test": "nyc mocha --exit", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "11.9.0", "dependencies": {"upath": "^1.1.0", "braces": "^2.3.2", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.2.7", "inherits": "^2.0.3", "readdirp": "^2.2.1", "async-each": "^1.0.1", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^3.2.0", "mocha": "^5.2.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "coveralls": "^3.0.1", "sinon-chai": "^2.6.0", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_2.1.0_1549394781328_0.49343502331118105", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "chokidar", "version": "2.1.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.1.1", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "adc39ad55a2adf26548bd2afa048f611091f9184", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.1.1.tgz", "fileCount": 6, "integrity": "sha512-gfw3p2oQV2wEt+8VuMlNsPjCxDxvvgnm/kz+uATu805mWVF8IJN7uz9DN7iBz+RMJISmiVbCOBFs9qBGMjtPfQ==", "signatures": [{"sig": "MEYCIQCx8r5Kbw7Wf+qHZ1jvICfq29iTOw2b1/PuhTjBZ3oiYgIhAI48aZ/eS3XgSmTejlN068v6P8Oi4eHiCtbeD6geStAd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYe2NCRA9TVsSAnZWagAALZYP/RgnhJUkeewbeJSXOmfh\nzBAJ0/FSIsCVt3ZbjI5VoyQJEde/mHYJc3BvrtlYN5XFmqvzRoMyGqICVB+a\niHsPf/pNyYriHfyDRZOY/LNX4fCIFCyB8VmkQbMGhIuC18YQOJyq9Tflo4Es\nZ518hqaoE155LBxpsPeQXt+sIazUP7lpuL/SxGfxUgCJ5iILQk0yzQ5vedsl\ngUWtQkC84zMaOL5SN1FYQJ6CGfAtC2JSgjIB7QOUlOolbKxhP7tmG+A15pOD\n1wyY93PlMu9D7kgnBKCw5gHEYyHE+MREINyBdIiSoBdB1rreufNPlLUd7Xuf\nA67GJtIrIL5BDdMgVuUSXAOWIyeJtAk6r0Ijk3KT3uNCWa04utFhO21jj+Pb\nAznxFjtS/NcTtpm52dpvj3xsgzsq7nlAyQPxuzXkuwfpxsAUVFcyczzkxlXJ\ndAU7v2cu8e9CzRwN63omfYY1ua8hDqfAB0AfYkNR5ubsyslwKCDpTzNECzW2\nlGm1J9k1MtSHESNSlo7r+ugMJ16NbSOZYY5SdpLb21Jmrt2ZCXionFlshWBy\nXIsdmy0kZr+IHHKzSPm0Bsh+vT4Mo8t8cx9LvgSq3Wm8Y/ZWCJhXGVfPHoY3\nme7em91u5Dmq5YttYbqqsjUeibn1IBGTZDeQWMvaEA/Cu3rDjdxSG/VfaZtG\n9ASF\r\n=0qCo\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "d34e55e04e904441c9ac9df9c5cce6db9fba0e70", "scripts": {"test": "nyc mocha --exit", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "11.9.0", "dependencies": {"upath": "^1.1.0", "braces": "^2.3.2", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.2.7", "inherits": "^2.0.3", "readdirp": "^2.2.1", "async-each": "^1.0.1", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^3.2.0", "mocha": "^5.2.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "coveralls": "^3.0.1", "sinon-chai": "^2.6.0", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_2.1.1_1549921676540_0.7050955611597705", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "chokidar", "version": "2.1.2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.1.2", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "9c23ea40b01638439e0513864d362aeacc5ad058", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.1.2.tgz", "fileCount": 7, "integrity": "sha512-IwXUx0FXc5ibYmPC2XeEj5mpXoV66sR+t3jqu2NS2GYwCktt3KF1/Qqjws/NkegajBA4RbZ5+DDwlOiJsxDHEg==", "signatures": [{"sig": "MEQCIBoK9hBiJqqUfZpbb9P8HUWSsO2KRwv3qxi2CXDZJTTwAiASRRoh2Eal50e1m9VmkUOB8OrLw3Nj78oSaKjxwXbJgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcaw3cCRA9TVsSAnZWagAAFrgP/R82KnEDk7H3Ze9dPJL4\nN1lN/eYSTD71v1XOZc+IjdpTdD+HmCDJkRi90pQrbsl8/NcFZDd6hzNYPlnJ\nOWDxQtJVz0+adIupVphpsvKjCtUIxpzAqMg1DTaVuMw+VtC3LlF6c9G/FuFL\nJR31r2SVbgeNoz9o4v+KKc9QL7WTwXEC+R6IGtU0VEpVrbQH50o+nUFGZtLR\nUgAw62TZpSJjVJtseQde8cuQMNFNA9ksCiOfJsPVYSsgOst8C65J8KWmPyu/\nHNBOvrPsOdkQ9b9w8y6VToxjciIU4+fLVFuSZ/EPcSZVXXsQ+Tw5hAEDS2TY\nbX5qJonvM2b5/zjrMZfhkVOQ+iQskfwBqFZq0HXVALylEUj3uhGBm2jiVieG\nGBSfIIr7TqDOts8zNXsrgH5JgsHgGhDYIqv1QL5R2sMn0VP8Akmnh+pPivIf\n/4VhJMXvkktAH7g2/scyQ3UYHtcpyEqPtgm2a5SD+jQGByVGyHYqyVNEssfq\nHdrnjSnw34wqBfYhXHWeEm1Br5yG4Y85bHAwiXVFfzipXUZoSs91+hWxHa+m\n6qkYaOwokPi38+AVNqKxoLbmbUHIri1cXje+d97XDYl/l84QdgMYbnYzNbrF\nepPD/DKq+7BbEhwyK1eLXdHn1s2NeOTa1A2jIuu+AWF4qXwnDR2089rjCNNv\nXH7O\r\n=/92u\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "gitHead": "20cb7672cb25a843df4d86475ffa8e4a8dc704b1", "scripts": {"test": "nyc mocha --exit", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "11.10.0", "dependencies": {"upath": "^1.1.0", "braces": "^2.3.2", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.2.7", "inherits": "^2.0.3", "readdirp": "^2.2.1", "async-each": "^1.0.1", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^3.2.0", "mocha": "^5.2.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "dtslint": "0.4.1", "coveralls": "^3.0.1", "sinon-chai": "^2.6.0", "@types/node": "^11.9.4", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_2.1.2_1550519772054_0.28873295114579767", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "chokidar", "version": "2.1.3", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.1.3", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "b2e65fc8cbdbb0c57a7d8147ad20077800ec60db", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.1.3.tgz", "fileCount": 7, "integrity": "sha512-Zkdgeb8pJCL4FG7fCIkD1ua1SENYGAqKQaqyMxkgVnU0IIWthjDVbSgcW5OY1IzgJ/Aatqjgl4O9iTZUeEJK5w==", "signatures": [{"sig": "MEYCIQDhVlSXAmSSvQretrKPhom2eSBD8W2mNf8klz6TodavKAIhAL+tKUB4wFNJkk7jvfi6I8VJc5ULAMeLlOKGmRyqwK5B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJclLROCRA9TVsSAnZWagAAn50P/ijlq1NZGpL19poySDvs\nvwSLfO9ZCi7EGawqApbfNGOFcgQBGpxP1b3KSa38I+c+l0DsEn7mZgjcsDaP\nqNcM8k2vuDH7AFkdB7xkc9Oa2byjUEbg5v6h5iuMnL4aw5ga9H39iVqYEycI\nVsOV073VRnl9iB7Pu6pULsguhuRYLjsH5Sm0Yzv8d7cl8pLNT9dC2K8tFXB/\nzIcik9fc1YvLWzVWAroPywF5JlfOG3CkLDngOrCfMf0hs3/zEzXQ8EFI6ifR\nUmbqgHien4QICou7oYDKBssq/D1Ak86b/xTZIBAhHkg/k04CUbiUCz8avDY5\nnZkEzrrSex12GpFZo2sz0hM2tF4j6ir3cq3d7RQvkXFPF8y9+R/A0o15xxlC\nkX4mPuGV9ToUTmInazCpRbmd6XmXtc6z6ZPG6m6s65HFBXiGrepoeo838m/X\njCL1qTaoDvJ6zRvQ/w6nNnpE67rqJrVXLRwiAQ8wrah1YFKxSl8t4WracWNJ\ni0YHdyFaI8X1m535QSpKgZnixNdeRNALu1mCYdbEIwphA5PNSKrKAwM4FOa8\nPrDF6RKAPJ22GNMLIT25TrGc9pax6Alh1M2sLG8kCmxDmPbvwh+99br7Ahkt\nsQcnDa0bn9353UQWHZJ3xT//vPPxBUIyXg2UxajCD+817qzF1+Hr3ekdk3YQ\neBZl\r\n=pbxm\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "gitHead": "1ea6388e70dcdac030f9c56af1449828b5d373a9", "scripts": {"test": "nyc mocha --exit", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "11.10.0", "dependencies": {"upath": "^1.1.1", "braces": "^2.3.2", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.2.7", "inherits": "^2.0.3", "readdirp": "^2.2.1", "async-each": "^1.0.1", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^3.2.0", "mocha": "^5.2.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "dtslint": "0.4.1", "coveralls": "^3.0.1", "sinon-chai": "^2.6.0", "@types/node": "^11.9.4", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_2.1.3_1553249358067_0.31018037332078285", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "chokidar", "version": "2.1.4", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.1.4", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "244a65d68beedf1078c2f193cf2735c2cfdc7da2", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.1.4.tgz", "fileCount": 7, "integrity": "sha512-ZVuFiB9IGOHqu+Jh7B7fSTmzsfDmUtC6yqd/V72UPQeXbNHONbMV0chOXtLXjsP3NvdUsHTNMg02NtXPDaSmNQ==", "signatures": [{"sig": "MEUCIHZWHTZELkIYXcCPJyFyLqtiMfz5FH2HW2AaqMruJWGRAiEA6iKeQLVEK+tCEt7oXJ8sbs8N2QwAR0Wntpuzn6iQOvQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86840, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJclLT+CRA9TVsSAnZWagAAec0P/ikUiQJQFwLWRi43qxiy\nmuquTKq2hrrbqyxoOio4tTctBW6pDqRBx4E7ocC2nschodokFl4XSCIQXIwk\nUvZlyw5JYrpKVOxQOdHZan/7zlWKqXgI+eX4ydGnrev+VLz7DhSlAh7y1CXQ\nPokiFJSKMaRJ6CWkJdKbKhTAFuvcrbtJUyYvVG5uc2sL8NNhS0uFevexTkPW\n3QAjPShcxCNa1r4iKQdPgeQ48QurqfRQk7JpNleg0mmFz5ON8vBZmabPx0qY\nHzm9sSa6Os4IA+2gR5E8eT0r0FSYFjIchdm4HNyCQT3WPqpg4GmZame+NSq2\nyMZGvdti3m4CUu7xrDr9OFdN+Cs7YARO1a3d9cGNb4oHBj8O7N28vBjP0Bor\n0bNDWmxM2u2ZlTWPtsv8OSaCy+LFz0EsUtRQ8TCE5Ao+Z7oxhsveo1mRWE/s\nkwFG00KvCCdEQVx8di0elPWZExHHhCZsoT0uJiNadry6Rc4rCZ1pqdL9ixMl\nX32iEd1y4KVGrZLu/tEMpyDMQuuZTrZTcKelrN4KCUzLUoRApDoa7GNSCD5T\ndpPyIU4o24bExiYNUl1kVFCFo/DUyM7LB2ByzMIHlaxdObmm4J603P7q/lzF\nPE9efr7/cMO6uMbBv7WWlT31Mym5WS1Wgz1ZXW5KiSj/JZoMaCtOdaQ1zLFA\n1Hm2\r\n=cUbb\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "gitHead": "fc243cd96206663b2f6b0740a9536e401dbd9337", "scripts": {"test": "nyc mocha --exit", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "11.10.0", "dependencies": {"upath": "^1.1.1", "braces": "^2.3.2", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.2.7", "inherits": "^2.0.3", "readdirp": "^2.2.1", "async-each": "^1.0.1", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^3.2.0", "mocha": "^5.2.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "dtslint": "0.4.1", "coveralls": "^3.0.1", "sinon-chai": "^2.6.0", "@types/node": "^11.9.4", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_2.1.4_1553249533706_0.7405741868691271", "host": "s3://npm-registry-packages"}}, "2.1.5": {"name": "chokidar", "version": "2.1.5", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.1.5", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "0ae8434d962281a5f56c72869e79cb6d9d86ad4d", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.1.5.tgz", "fileCount": 7, "integrity": "sha512-i0TprVWp+Kj4WRPtInjexJ8Q+BqTE909VpH8xVhXrJkoc5QC8VO9TryGOqTr+2hljzc1sC62t22h5tZePodM/A==", "signatures": [{"sig": "MEUCIQD5k5iF83OWgOmrD0oAjBk0b1Cm9BbiHxaj4IkeHOEJFwIgRVUzFPegLXiidFZ3dce/w4YupdKcptiavFc5pqBfYiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87443, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJclVEgCRA9TVsSAnZWagAADIwP/2n0TNsMPojcTrQNdJ3h\nXoBvI7KPlNmaom0XeZHkujkSwv9VG7Ml+rnDSclfWdK+V73++TxhJub3QDRi\nkpTwQ7bv/FatgYuH8NlkaQXAuPDH/7IYAF/ivUHRqth2geIWUViQ/6w5m55r\ns4yPlnRYqeAmzBUTCfDiQBReHpQCYFob46NXXI7gohg5koW3+vTPM4bFVMpT\n+5XGVEUlSdQ36QqqNTnBSM0SqVeHQ3kVgqvNuaojwxKcZrqqHeTd8NnA7LDg\nGHNBLBabIs2MW3/aTpHUDqYZ/3iHfa4nNiiSq8DaqeqYlRyyBpJocD6SB2v0\npkqZbFH8mx+JQQ8HtIE1vNjXtN1ONhhXa8nTrfVYCoczNeqrn2y0iB9IPVnl\nY4C2WYT+Vd+SS+ZizjxKyYhBOrB4pajSwFNt9NoW5TRAWy81Nmzk0nB7oYy2\n17TOYJgXIAan2/htuoXetM9dkOSzkSLKnw1hQGmx2VuQmigA03E0upW+0WV+\nJEheMpBnV3DrjIkGHEdNl8RS0y8StAY0DULQQLzf4aTrdGwLxlED8mKe0Bzc\nKAHLeAfTZwPrOSRbM/UIs/eEN4PTgIhgh/fkU2+8is/aFCeTQi4kxtPYPVoi\nslzHePIYMgdwIRnKF2P5qUPtx6ChDcPHPnpjNlT86z5J0gjSJFw/51FRRKeD\nUsBh\r\n=wXRO\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "gitHead": "3595b42056c9082417f6cc9427a82a8f481352bb", "scripts": {"test": "nyc mocha --exit", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"upath": "^1.1.1", "braces": "^2.3.2", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.2.7", "inherits": "^2.0.3", "readdirp": "^2.2.1", "async-each": "^1.0.1", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^3.2.0", "mocha": "^5.2.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "dtslint": "0.4.1", "coveralls": "^3.0.1", "sinon-chai": "^2.6.0", "@types/node": "^11.9.4", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_2.1.5_1553289503798_0.5668650651093496", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "chokidar", "version": "3.0.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.0.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "6b538f0fd6d5d31d5dd2b59e05426bec0f49aa40", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-ebzWopcacB2J19Jsb5RPtMrzmjUZ5VAQnsL0Ztrix3lswozHbiDp+1Lg3AWSKHdwsps/W2vtshA/x3I827F78g==", "signatures": [{"sig": "MEYCIQD6tie9b3Td4/VtjeLmqc6OGWeu2HHzUbpZUzq2DI7szwIhAJbNc51cAW2lgw6h22JvmfNZtR3mcXHL1kqFE44QgkCD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyH1cCRA9TVsSAnZWagAAkZkP/iKwS/1cA2j0YQdd19AA\nmAOOIaIBrx+BsksdTWmr0U7MfusuKI4keyS7C7gQ4ZLPccBT4Eiqzo8JHlKT\nu8u303kd+MOEBxbm4MVu3CJZKtuiFKz8eN1gfrMWmgye1rEYkYK7hclHNRXq\nT3vxbey7jqem0y0/JuBsU/4wF0AQfLPl/EJkXZD8woBPdxUOeGSed3KQGjw4\nZKApt6eNfuST1jAZU6XMcZKLYimGs+XIqLdq2jOrOCxDQPLWkdlW3++47NGK\nWZTdOD5WRvP6zy2bJwhKMiU2cB1d3MSYqxJDhyq0xRVsYQzXUqNN9zG4BaoP\nXkTkmYbVd5/4fufyC3FY0BEL5UhR/DKp7DRr2wa9+qDgQKFpfl4r6O9ap1Oq\nGA4ISClwVvYQ5v7BLuZwh9oD6Ek1jdXzlDEFDmyaQ0pbbmWFNiVOr93AIOWB\nhxvz7ajTAW3OP4a6I9Ex4gqJ2dgf8x3Ef2DgEPoTqCMLxWszR98xrscAfbg7\n5hlgmX9+q08YOZrvs/43AP+CcXnailBx7g8SJM2txju3kzcK2qBvdc5vEH52\nCZco9TwhBsy5I4B+VUgogjSdlwL9Maf5ujDy2Hm4e2hIsqUJv5ClCKFijg19\n3Sn5xHbhXc1+mV24CdhGrxNjni/RKrrt0gsvTY+x+KY6rSD5Kykyz9ao+l9u\nS+Dp\r\n=Tau4\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "7b8e02af255f54bcc3978f2a926cdc0b0ca107b3", "scripts": {"lint": "jshint index.js lib", "test": "nyc mocha --exit", "mocha": "mocha", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"braces": "^3.0.2", "is-glob": "^4.0.1", "anymatch": "^3.0.1", "fsevents": "^2.0.6", "readdirp": "^3.0.1", "async-each": "^1.0.3", "glob-parent": "^5.0.0", "is-binary-path": "^2.1.0", "normalize-path": "^3.0.0"}, "jshintConfig": {"expr": true, "node": true, "curly": false, "mocha": true, "predef": ["toString"], "bitwise": false, "esversion": 8}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2.0", "mocha": "^6.1.3", "sinon": "^7.3.1", "upath": "^1.1.2", "jshint": "^2.10.1", "rimraf": "^2.4.3", "dtslint": "0.4.1", "coveralls": "^3.0.1", "sinon-chai": "^3.3.0", "@types/node": "^11.13.4"}, "optionalDependencies": {"fsevents": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.0.0_1556643163624_0.27595207483280304", "host": "s3://npm-registry-packages"}}, "2.1.6": {"name": "chokidar", "version": "2.1.6", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.1.6", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "b6cad653a929e244ce8a834244164d241fa954c5", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.1.6.tgz", "fileCount": 7, "integrity": "sha512-V2jUo67OKkc6ySiRpJrjlpJKl9kDuG+Xb8VgsGzb+aEouhgS1D0weyPU4lEzdAcsCAvrih2J2BqyXqHWvVLw5g==", "signatures": [{"sig": "MEQCIG4xD6ZSLRIPt/e8SOn+QBp8OWb2Mn38ljip3W9F3q8KAiBPhGt9L77KDhOBO+sIq+iJz4KE6xzwlz+vg13F8Yk/EA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3AewCRA9TVsSAnZWagAA6V4P/R93bPIsuabYXXrN0eqD\nlf6m0ybcjaxIoskno8mKB4m+O/uAk9RCfu5az+Gkf6eJh3ImPerNnIQuQV+D\nGzXMSulFWF++B2YjXFcaIPb4RDflJoVf/jj+lKW86pJIu3XiVnc1w8Q/3fiP\n4U8ifqSEXeToKcd6umSxkWHyWfOqzAF8ZeMz0Cex+Ix0J8SxelTn3XdvhCh2\nAQEhsFIDi81UOXht2ZuUmQD3XCbe1L0iqJIQB6h0NzygnIPDvbhI5dwo6DwA\nfwBTkbDG3UaldVVjTAkDgUHp2YaYXETPkv5qz4Dcfo6aH+8rBACgFTg8HuxA\nZPkzFikhfw0SUTa67ZpwsovdPa9IixHom/JmBm7tOFR7Oe/FFoavzFALYoBu\nTRg5neBFyf4Mx/qw7Xnooe1XVHnOO9tbOyJa+1WX4z0MLw8Snlki9uetZC0B\nKcWrLq1ShtNTEFinm873HJ2RSlaOXA7XPPlXEsgsmv/L6qE3hAUebWPuly1l\nVOX5F+S/LnEjjHTKGOZaO0sNdtNldOpQnghkQSgCF/iIGGu6Al1iIozA96v1\nxulsoDEJSrcn6gu0tyfLVAADy3mRr/pLDVj2EJJX6WymYU49xCyGGP/sjsnI\n9Zb9Zojyj0SweAiiSkxSFl4gClSu8EeoErJQN2CTSuPUbhFt7Kf/0pHEBl/L\nlwp1\r\n=gXaj\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "gitHead": "8f5be3c872b2dc9115ed5062d95fde2844eb6a9a", "scripts": {"test": "nyc mocha --exit", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"upath": "^1.1.1", "braces": "^2.3.2", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.2.7", "inherits": "^2.0.3", "readdirp": "^2.2.1", "async-each": "^1.0.1", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.8.0", "chai": "^3.2.0", "mocha": "^5.2.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "dtslint": "0.4.1", "coveralls": "^3.0.1", "sinon-chai": "^2.6.0", "@types/node": "^11.9.4", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_2.1.6_1557923759951_0.08921357795773344", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "chokidar", "version": "3.0.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.0.1", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "98fe9aa476c55d9aea7841d6325ffdb30e95b40c", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-2ww34sJWehnbpV0Q4k4V5Hh7juo7po6z7LUWkcIQnSGN1lHOL8GGtLtfwabKvLFQw/hbSUQ0u6V7OgGYgBzlkQ==", "signatures": [{"sig": "MEUCIQDuGvjLQ/FDand4cj2AMl5HaOC2I2sZAcGN84d+xlUZSwIgZFjmdFTpxpr3iUvpqKYfBH9i0FZKajeWZYkOR7HC/3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81090, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9EYVCRA9TVsSAnZWagAADtkP/27LzWHPxixtrY1aF1cO\nhpYIgs5OiIV6as8JVl8WB1HX7eJrroNU81pquue5x/erOK4/MflT33rLUVRe\nwYpVNi2rkkfVe9DCwvvambLCQWCz0qhFaOK5KQBmFwbZnyLuHkBDt69ql7Hp\nbsPFRTAd85hcATrf+01xQ1Ed+N44hUjnKQ0zLm75NaoeCVM3knWPVX8ckaov\n3iOgaL/wVQvk157IKJ5WFlpBIX+dk0TpvXpofhB/EN4qhH+ByR7Z5xqT04FK\nFha6/Ct8O5dved6huRrP+6L1ax9Z3kf5rM2Hc9FvWeQ0r58Mq2j/XqK/tyPl\nd7D1Dy1BqSZ3Lb3ZLCGXTcsq1thY1kRG/AIWhKoNPL7HHPEeOnktoSTAD09X\nfOl1A7McSbhm9IKRK4NXQaTI9Ayb61J5X9XxTXzeJZQczoJp6rR1mCTVLYMr\nPkDu7eQquHjS1179XhbRZPCmZhgeNsgNwZBnIMRKUjXPW8lRpqvlroNa/Mi+\nXGYO3y+wOS+szWiOyZU0uYtWZLVLZOHuyuM8JeQSZJj2bLcYDv29Cb/5P3wP\niCzbFPxQtzFKLmFSvTIEG5I9vCdgmmVVjh6otq12louExFboMGzs7xpQaQts\nXP1ZgiWFRIf6UXhUuq/ckpGgOclkRSFyV/fTNmnE0ysJy0yhUja16iDykBc9\nv8z7\r\n=fgXE\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "b9cd3eec337c95b27a4a6616c89a95465bb1a887", "scripts": {"lint": "jshint index.js lib", "test": "nyc mocha --exit", "mocha": "mocha", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"braces": "^3.0.2", "is-glob": "^4.0.1", "anymatch": "^3.0.1", "fsevents": "^2.0.6", "readdirp": "^3.0.2", "async-each": "^1.0.3", "glob-parent": "^5.0.0", "is-binary-path": "^2.1.0", "normalize-path": "^3.0.0"}, "jshintConfig": {"expr": true, "node": true, "curly": false, "mocha": true, "predef": ["toString"], "bitwise": false, "esversion": 8}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2.0", "mocha": "^6.1.3", "sinon": "^7.3.1", "upath": "^1.1.2", "jshint": "^2.10.1", "rimraf": "^2.4.3", "dtslint": "0.4.1", "coveralls": "^3.0.1", "sinon-chai": "^3.3.0", "@types/node": "^11.13.4"}, "optionalDependencies": {"fsevents": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.0.1_1559512596104_0.015377993694621894", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "chokidar", "version": "3.0.2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.0.2", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "0d1cd6d04eb2df0327446188cd13736a3367d681", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.0.2.tgz", "fileCount": 7, "integrity": "sha512-c4PR2egjNjI1um6bamCQ6bUNPDiyofNQruHvKgHQ4gDUP/ITSVSzNsiI5OWtHOsX323i5ha/kk4YmOZ1Ktg7KA==", "signatures": [{"sig": "MEUCIQCRa1Ar3u4cKoBnaNq7gXTR/JpNk97au27C4DCjy4WeUQIgaYcPQp8yPzffNgUBzuTv5NV5QcvykFdQ1Qp9ZhVXfts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdITdHCRA9TVsSAnZWagAAwlcP/Rx/WJu3dAt5HmeaPpN8\npTlITljHJKOjbtiwau+TySYYdO8JmPlplHdolJ73p5mBLYu3TUy8DNd6vb1f\nwPh7jMxV5Tff4LHonf6S+P9tqOmTm1EZP5hnFkRKXApTwhO+mAVN8W4MLo6T\nGgIKRMeg37WaMe20LV85H4SswTP+M+N8UikuJfBnZEAY/u1UUmHGj4bvbsou\nflogbqsnVk2Zi5QO8KBAXp7boDyWFT+xTHOkmaCuTg9KmjTBX2w9xVM+dB+Q\ne1Po6bn+oEfTCJ8FsUNM6dF6oV9WV3tfv6Bl4EbPjGnqLS9riCtPfnytTXZ5\n+zv5i5s8j4lnCfbec5vEZRHrgrGjNXgNpNc6a6Q7eOkuXv3DsTOPLwFHcsaP\n68ZEXNUElEb827+LNFcUE2FbmujNuSrFX0Jq7qv+x/aEV9DfJG9/QSzGs1yM\nngUuZhMz/7+4tjp2O8k1tmDt3BOgm2tl4q1rIFduhHxslYWYS96B0KlNjqGR\n8Dwl5WHnM5nskJ2RQqGNbSm/VyIr5pNreIHpW1hL4LEweAN66ocvL5fqslnu\nGS9aCfv00U+EtgyV58v6jxmP8Iv0UdNpFGAx9IFFYdzqXwTlrjWsv2wknHOt\nV5AbIlkmq5cLcij28y0mv8PnPX02bV8OBcLpu+xqt02ihrqevcVDOoBPUnfG\nlZC3\r\n=P/Sx\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "589cb020967dfa6c61e7a08510ff39e4e9e2e4b5", "scripts": {"lint": "jshint index.js lib", "test": "nyc mocha --exit", "mocha": "mocha", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "12.5.0", "dependencies": {"braces": "^3.0.2", "is-glob": "^4.0.1", "anymatch": "^3.0.1", "fsevents": "^2.0.6", "readdirp": "^3.1.1", "glob-parent": "^5.0.0", "is-binary-path": "^2.1.0", "normalize-path": "^3.0.0"}, "jshintConfig": {"expr": true, "node": true, "curly": false, "mocha": true, "predef": ["toString"], "bitwise": false, "esversion": 8}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2", "mocha": "^6.1.3", "sinon": "^7.3.1", "upath": "^1.1.2", "jshint": "^2.10.1", "rimraf": "^2.6.3", "dtslint": "0.4.1", "coveralls": "^3", "sinon-chai": "^3.3.0", "@types/node": "^12"}, "optionalDependencies": {"fsevents": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.0.2_1562457926511_0.8943203775932804", "host": "s3://npm-registry-packages"}}, "2.1.8": {"name": "chokidar", "version": "2.1.8", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@2.1.8", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "804b3a7b6a99358c3c5c61e71d8728f041cff917", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-2.1.8.tgz", "fileCount": 7, "integrity": "sha512-ZmZUazfOzf0Nve7duiCKD23PFSCs4JPoYyccjUFF3aQkQadqBhfzhjkwBH2mNOG9cTBwhamM37EIsIkZw3nRgg==", "signatures": [{"sig": "MEYCIQCEI26gktJj08Z1lqyiYuPimjUd6qnToXYVwq8iFg7+jgIhAMbIBS8O3brX5FJy8cJru6klNzIx9qkE0KjS7NXr8ZgE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87828, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXYPqCRA9TVsSAnZWagAAD3wP/irJQlf2kbB6XF6wRa9I\no6DIDY/XukhWwlXuvQfxnmCmosflH3bq+aXMZK5HPFpfDWHayJ8P1YV2ypiE\nP8cfG3iDCnE0S8TpO73etXxaiUT2NuLBvAhypBWjZS9S4kZ1jp+oFxCs+yG5\nU+ALkILneYpHVCjEmRZkGEKnyYrQYwfDugd89u3Ue3DdqmSlPlm2TQUGi7pz\nfyK4By1+f1Are7SIabOmKruyoyN11Qj3avwzKXjYyWj5knaRm03s7siSZG0p\nFqgrzZwXymIO5qS5nQaEP/F7sbgRG9rzr2s2aKZf1NtOSfmWQn2OiHRBM2aN\nh6IWj5/UfVR3sViLqXwTf52BueJSonGsNhEHeLAxsDDgj62VRqj/tUWFR5Os\naeCf8aNb+HLz/PqLRxtfCGmU0z8ar6i6CWpxUzlZyZqNMDl1gFeR5d7x5Evn\nf2xSQ6/091jx+65XDoapQML26KuGlbt5s9IQLeP+oG4MtYbwcFnsmfaN1t6S\nKt0cL+R0HIGOT3+YiZxUkZIk76I0FVdW0vxgerNdaASubF7Epq1ftNNYio+5\nEsr0OWprSiXlzHH+3Y3L9JAldf3GfW9KPDpTgJorhbrzw0JEuV6z/R5I9Ces\n5naB6w5EPPnhSvhL6J+7Tm6+ge1f5f+U1/+A5QGv8paQYkRH0rvZP2735q3R\nNyeK\r\n=wtk7\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "gitHead": "a60961966746b143c1cdab13b37a90b97c4fae05", "scripts": {"test": "nyc mocha --exit", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"upath": "^1.1.1", "braces": "^2.3.2", "is-glob": "^4.0.0", "anymatch": "^2.0.0", "fsevents": "^1.2.7", "inherits": "^2.0.3", "readdirp": "^2.2.1", "async-each": "^1.0.1", "glob-parent": "^3.1.0", "is-binary-path": "^1.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^11.8.0", "chai": "^3.2.0", "mocha": "^5.2.0", "sinon": "^1.10.3", "rimraf": "^2.4.3", "dtslint": "0.4.1", "coveralls": "^3.0.1", "sinon-chai": "^2.6.0", "@types/node": "^11.9.4", "graceful-fs": "4.1.4"}, "optionalDependencies": {"fsevents": "^1.2.7"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_2.1.8_1566409705431_0.31447568058522024", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "chokidar", "version": "3.1.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.1.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "ff23d077682a90eadd209bfa76eb10ed6d359668", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.1.0.tgz", "fileCount": 7, "integrity": "sha512-6vZfo+7W0EOlbSo0nhVKMz4yyssrwiPbBZ8wj1lq8/+l4ZhGZ2U4Md7PspvmijXp1a26D3B7AHEBmIB7aVtaOQ==", "signatures": [{"sig": "MEUCIQDVEq4vkObgPwsvd8wPz2YQ6RhXONjL2O86XIriMife0AIgQNFJ1LIww90UsRzgSmeakD4Gou/A/7xT2x8KVUJufEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdf93GCRA9TVsSAnZWagAA3mAQAIdYIcgPGTa+AE2jsa+w\nXQhjlks74vNQ5H6wS20nOGGNVJ3rnZh9lDSM0NBwmWafgNTiTLL78utTtw9R\nNfzx1kSArdD8gF6z3l3qeV0CCWYwUhjHZ8rLrx/7Nq4X7j3S/dv5g4lJ1KLX\nNkxQWloh4BDezRCXx76FxJk5UegKHk+hvdKMjlP9jPGz/F/lvsS8fnjNky5U\nEo/6/stg47jWUPpK0J4FcrP1FyZCwZk7MKvYzAzEgoRCZAq+utpkHe3MAf0w\nbuyuboPWdmIC4c0GA5BGKJEydLxqHQfY9j9alHUAS92IxR7W/ijY8CVdxCIj\nWxsexbMc74KgDEz5OVr7+SPayuo63JKAx9qvw8v2ymamlrmj0RXLj7T6qEP8\ntlqL1ypPCB/dbqEOjGSj/faf7kdVKSUvHpjdTJ8jBUya1u1VyXcJmyeDgFkk\nCjpGFvY9lTpDLMhzxKM7Zz858918DqaOvzlRxGI8HQt797zue7S3QZIb3bql\nT01ECRbLdxeze1hphB3y7fN2X2RYHtsIpIzF1niFpuoLuDmoNjd/pv1tYeTf\nY2THtiquMgjblImu3EhcaHY7uIYEQN7tsU9GTz8SkBxFFV+HdC54jo9DOK8G\nbILEToXmb5KAkUbAH+A0hzNKaxA9dKR7C0LDjYtq1Ltv+WML7Xsfla8BEyKZ\nPini\r\n=PZWY\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "c2ff758d0484c8e5b514df145c31cfae2c2314aa", "scripts": {"lint": "jshint index.js lib", "test": "nyc mocha --exit", "mocha": "mocha", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.11.2", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"braces": "^3.0.2", "is-glob": "^4.0.1", "anymatch": "^3.1.0", "fsevents": "^2.0.6", "readdirp": "^3.1.1", "glob-parent": "^5.0.0", "is-binary-path": "^2.1.0", "normalize-path": "^3.0.0"}, "jshintConfig": {"expr": true, "node": true, "curly": false, "mocha": true, "predef": ["toString"], "bitwise": false, "esversion": 8}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2", "mocha": "^6.1.3", "sinon": "^7.3.1", "upath": "^1.1.2", "jshint": "^2.10.1", "rimraf": "^2.6.3", "dtslint": "0.4.1", "coveralls": "^3", "sinon-chai": "^3.3.0", "@types/node": "^12"}, "optionalDependencies": {"fsevents": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.1.0_1568660933836_0.1661031142918583", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "chokidar", "version": "3.1.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.1.1", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "27e953f3950336efcc455fd03e240c7299062003", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.1.1.tgz", "fileCount": 7, "integrity": "sha512-df4o16uZmMHzVQwECZRHwfguOt5ixpuQVaZHjYMvYisgKhE+JXwcj/Tcr3+3bu/XeOJQ9ycYmzu7Mv8XrGxJDQ==", "signatures": [{"sig": "MEQCIA2K3Rq8eHTAWW58SS3lgzEAslFYgYIgHkamMd/5dE+mAiBcnOuU05Hd8IxsjvYT5oaVqmq59Mqb2VGH/X6mcmn6sw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdg8kFCRA9TVsSAnZWagAAQRcQAIy8I7/70zPvNmo2dfMn\nESnmzDxE8Dhb027+PROgAMwrb4OTJ01SY1qePj8086MxWMGeoUaE/coRs1qz\nb3M51oqC6Yexn4wpd9lG3nPyrJEZN7v6A2G2DBR21brg0maLq4dcleLUfUCs\nwb2RJyz/3cO20jEOMEWGrJqwPp7JYVi+edh1IIQnWPA+7KucnwCB2IGisXh5\nuxg2F2oeAX+4kqPsv+hVQGVql3igKCZiasTBXMx4As1vKYLENt8t5bn0PF+N\n5RVjsxHMgM71QMl60RyFRYeTwKXgzboR4oxHJL6s7mm7HklQbPMtALzcjLlK\nb/2OrSiiV6zVxiPd80lLe2GJIC7ccxT3QI4NJ4Gbq3d150wgiJupoiE2t4qP\n8P1SDbfqrhcuchf1+1EtMLpZxkEOuu5DjG8yS8T8YEA1bPw6irnpVJupQzBQ\ntqKnrEovNeEvA7bma+nADsbzdkVUEaU60D99MaLARMrYzP7FNcvjHn8omAA6\nTCpni9OhYvEcq/f9qGRDveu4bP7/+uC8YS63BGBUSpKe72HZWqu0EF0Wm1dX\nQK21OA6yRPz+nP0YAskH0D5QiQPUCPSkgioCEBvxY+81QinH5E9gahk7N/em\ndsp0EoFhXnJ/tzdZjX7n50f1+us5FgGySb0kAlKaaZzyn1SKxHnbgETqGLaj\nGvsA\r\n=jFhN\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "31fd30afa4aaa7cbfa1d6172707da1f31b867223", "scripts": {"lint": "jshint index.js lib", "test": "nyc mocha --exit", "mocha": "mocha", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"braces": "^3.0.2", "is-glob": "^4.0.1", "anymatch": "^3.1.0", "fsevents": "^2.0.6", "readdirp": "^3.1.1", "glob-parent": "^5.0.0", "is-binary-path": "^2.1.0", "normalize-path": "^3.0.0"}, "jshintConfig": {"expr": true, "node": true, "curly": false, "mocha": true, "predef": ["toString"], "bitwise": false, "esversion": 8}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2", "mocha": "^6.1.3", "sinon": "^7.3.1", "upath": "^1.1.2", "jshint": "^2.10.1", "rimraf": "^2.6.3", "dtslint": "0.4.1", "coveralls": "^3", "sinon-chai": "^3.3.0", "@types/node": "^12"}, "optionalDependencies": {"fsevents": "^2.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.1.1_1568917764708_0.267458021257603", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "chokidar", "version": "3.2.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.2.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "a0b08f9b770c082def0100e0fbcef322ad156a8a", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.2.0.tgz", "fileCount": 7, "integrity": "sha512-ViJAmAatEHwGCbBa8coPyRY6yiQ+yryuHT9XnkV6l0ct140Tf6JguJ0LtgoJrjxr9I9utL8IRuqbjhDJAeWIeg==", "signatures": [{"sig": "MEYCIQCTAEvLX0DwVN0vqTCaRAx1W8fiWe3m9fRObIWcOf1wngIhANH9HMUYXAh4GhhCcUiFgBt9zqtK1ftTwqZkK9RpvTIP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkr1NCRA9TVsSAnZWagAAGhoQAJambKKqgEeg2znlc/Gk\nsLNwSFnrZTHi3qTUvNYgZ42sVSbLha993EXCLrwC7bq6KB7SZpk4gj+Ndx3d\nhxR1wqUpUOZb8C+jt81bwmWAB9HjXJFM/OLfgA2bRUiLrdeZDWtnWAVXzSd7\ngHLtf5BLcoWDCAx/ZKwH+GXo/50uvP3O8Gmy6nEawxUup9vc+XjJok9iGxPE\n9512LUzC5Nvus2V5Opm5A1zK3bAAR9gN7nI9Vl3QMleb1/+VIRzgtBabWkMR\nLoq0xoWF36c09nGQRqlVlf6MLigOxZ1Zq5psGNc7wHLCgVpipFfNaLyxQyLv\n/HOUSOqP7ZupxCmBIpt5eiGruPM6K3EeLGGBDXkBfBMDdOTYFEaiEAe7Qa5H\nyuvpV+GEQJqCjKpw5jUB2jJwyYPrOblCJEDTHEGcbZvh0SDyGoP+PzijIYCR\nCvlo8GpTsvgxHSSLzag4TRXEIps2L9C2zouRo4v/q0r+mOww3aSKiKKyAj9E\nl0HXzOWXGFdBMoSnFjaFkgi9Y1hODPldK76xaIL5CHdk/0fG39wkhqCm4Z1h\nWVrZ2u9QOO/Il0v8nZJZxJqhAK3y2rhw7LqpyXkUueeNO6MY/p3b9kr2w/Wx\n64iOCo6zJs8nvjqcnERKBdAyr0l9MUQBvQHHi3/6qZDtPnRo20DezqqeHWhL\nZ2HC\r\n=GeTf\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "engines": {"node": ">= 8.16"}, "gitHead": "e5909e33784cd93a9bc838fe09474bb221bf53b8", "scripts": {"lint": "jshint index.js lib", "test": "nyc mocha --exit", "mocha": "mocha", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "12.10.0", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.1", "fsevents": "~2.1.0", "readdirp": "~3.1.3", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "jshintConfig": {"expr": true, "node": true, "curly": false, "mocha": true, "predef": ["toString"], "bitwise": false, "esversion": 8}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2", "mocha": "^6.1.3", "sinon": "^7.3.1", "upath": "^1.1.2", "jshint": "^2.10.1", "rimraf": "^2.6.3", "dtslint": "0.4.1", "coveralls": "^3", "sinon-chai": "^3.3.0", "@types/node": "^12"}, "optionalDependencies": {"fsevents": "~2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.2.0_1569897804535_0.5269454388005954", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "chokidar", "version": "3.2.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.2.1", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "4634772a1924512d990d4505957bf3a510611387", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.2.1.tgz", "fileCount": 7, "integrity": "sha512-/j5PPkb5Feyps9e+jo07jUZGvkB5Aj953NrI4s8xSVScrAo/RHeILrtdb4uzR7N6aaFFxxJ+gt8mA8HfNpw76w==", "signatures": [{"sig": "MEYCIQC21vCfabUNnxJYhKDjzFCjC5jxYXGgLfh94ZKYyi5rYwIhANOqSUtwUbyL2mvsmXLlWPtKmBKgNDCKQPlpoJVE2C5/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdk26tCRA9TVsSAnZWagAALrwQAI+J1XJIeYiBPMEq1NmC\n6mVwYF5LRYoik47fkPaM0BieVz4BrmwnDU1Frob7G3mgIB3eUi5ny1EkhZp2\nAC6rYQYXQ37HQZWuPxrQwxX6qvfGXUcJ4sYPtqBxja+TucN7WcELUiyGRYcO\nJ1/ClqcTJZ0adYiCn1oSI8+bw63MzMW0dtxuwnnWc2HMUHadzZPrqcPPicF1\n11tCcPTlgCE4YVNcKgAknqTNwYbpGIFIW51nB6IsbbFxrzG3TgUtwvdnIwsn\nN94aXEGWSjR8AaNLtU/LKQmL7iWGzBz2RPRD4ARM1EcINlj+v3GxIp8j3waG\ncO5wnv7B9K7EP1Hbmb1nMMoV/LgARhYXrlIU4RPwlX+PxBcaX4dWffCqKfBV\n4bty0FgkVDZrDRPHwnM2Ub6+uOurlPGwQhg78UTajWnig5ovr63185SiOSmg\n2v3hfnGvk7B0ResVYwY+6Lvpi0ND5xM8y4dyhNfqmzo9i0NtpxAv+PQ3q2CG\n1wiy47fdb8fJTJxh8ketRHxwI8Znd97YoAFmhYPqtjXqjfOKuxnBosacIT/D\nvtQjEYbE1UrDIQcGb9w5hQ3tDfxxjjcOyqcoXHylo9MaDrbqvreLBtc7YImq\nzh46sReqfZu0oCphEAwAnatNm7DJUP3VKW8/LCh6I+lE8uLn1v9aaF6Lpqis\njk/q\r\n=njEv\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "465de991f6a1ec5afde1a9c55286f7a7585763a8", "scripts": {"lint": "jshint index.js lib", "test": "nyc mocha --exit", "mocha": "mocha", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "12.10.0", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.1", "fsevents": "~2.1.0", "readdirp": "~3.1.3", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "jshintConfig": {"expr": true, "node": true, "curly": false, "mocha": true, "predef": ["toString"], "bitwise": false, "esversion": 8}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2", "mocha": "^6.1.3", "sinon": "^7.3.1", "upath": "^1.1.2", "jshint": "^2.10.1", "rimraf": "^2.6.3", "dtslint": "0.4.1", "coveralls": "^3", "sinon-chai": "^3.3.0", "@types/node": "^12"}, "optionalDependencies": {"fsevents": "~2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.2.1_1569943212641_0.5641062101232077", "host": "s3://npm-registry-packages"}}, "3.2.2": {"name": "chokidar", "version": "3.2.2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.2.2", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "a433973350021e09f2b853a2287781022c0dc935", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.2.2.tgz", "fileCount": 8, "integrity": "sha512-bw3pm7kZ2Wa6+jQWYP/c7bAZy3i4GwiIiMO2EeRjrE48l8vBqC/WvFhSF0xyM8fQiPEGvwMY/5bqDG7sSEOuhg==", "signatures": [{"sig": "MEYCIQCHwQADiXotlibu6MAO+95eXaWry2Nzy7+sa+ZMiXSVCgIhAM1uLy15667UnLdiXBnx3IXneR+ymQDrdaHIKnKHoA4h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpoOzCRA9TVsSAnZWagAA4HAP/1nurF8Qye75bm3Al2po\nvUV7r38QSmvYS89RZ9vKtXZ4niMWFSNYN5zqro+qQ3Y4iJ5fEU4UBqjrASHP\n/FpN3HbZUIXTXmqVoQlV1BoMblnaYWrKHpnJo52VGzv1vYimTj5EeY+jWr5Y\nyQGA3keyMMf5P8VjQQsi2tFdgi41kWGhiGtZvZ7GZLdztT4NwRgcR78W1erp\nHkh1flNoRt1O10dYqrHFwymn+IjliHxYz7QMrjb03ZwC1IcIas2qb8VPut8w\nbTxKywhk3daPwyIKBYB/sR6tr0LP+a3FM9Tg8SE1jKWbrAxus5QETwDWKeIM\niiDqCxxFQmx44sBURB8TfiCmdt+2jF4uIwH5npXspsNNTONH3JA70PHH0SRG\nUATl1oyxUyE6rJ/0E76551uFYO6RHTE4BK3ewqt7Be5why5F4KkuO92QZ9Jg\nTxmygrxnXEcZCv/QS/L+ejv50iF75rEslVeA+f/VNm7sFMmz0oUzPc6wqHCj\nU8zqXXm8lB7nT3BP/lDEuGiQTBeQz5+pmf5vPzK5OVlWoDvJpkVdkz01pf33\ncWaK08h+qyTiQlZVZHni/y83X67Z8BUBGZIdaleCWzY6xDisPyUvvx2Cn+jV\nHIu7t3K0pXssuZ+8xVnMpJav7QYDoUVKIKq9VlTSpJD7GojDD5Kjp4+Kwd5c\nAaSp\r\n=W938\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./types/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "a3b19f1e35295cd2c0d118da8aaab9160da89671", "scripts": {"lint": "jshint index.js lib", "test": "nyc mocha --exit", "mocha": "mocha", "dtslint": "dtslint types", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.1", "fsevents": "~2.1.1", "readdirp": "~3.2.0", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "jshintConfig": {"expr": true, "node": true, "curly": false, "mocha": true, "predef": ["toString"], "bitwise": false, "esversion": 9}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2", "mocha": "^6.1.3", "sinon": "^7.3.1", "upath": "^1.1.2", "jshint": "^2.10.1", "rimraf": "^2.6.3", "dtslint": "0.4.1", "coveralls": "^3", "sinon-chai": "^3.3.0", "@types/node": "^12"}, "optionalDependencies": {"fsevents": "~2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.2.2_1571193778947_0.14158953015119358", "host": "s3://npm-registry-packages"}}, "3.2.3": {"name": "chokidar", "version": "3.2.3", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.2.3", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}, "dist": {"shasum": "b9270a565d14f02f6bfdd537a6a2bbf5549b8c8c", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.2.3.tgz", "fileCount": 8, "integrity": "sha512-GtrxGuRf6bzHQmXWRepvsGnXpkQkVU+D2/9a7dAe4a7v1NhrfZOZ2oKf76M3nOs46fFYL8D+Q8JYA4GYeJ8Cjw==", "signatures": [{"sig": "MEUCIQC+ps4cOVRM5VVklZDwuLTJc7A6Br0mNMZ/EPENItSqAwIgJ4bD4UOc6FepYn5bwOJZ79mczNK7v/pJif4zBxIptqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87585, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdtj/rCRA9TVsSAnZWagAASzQP/1b6KxOLq9919rAu9kmw\nw9r/eQydAEShJAGkOcwwZUA8nRaBolHjP/i9sageW+wPBYT3Bbqzd1NOD/Y9\nRUzKF+7LT6kqSIJWUppyOLI8HS3bhjqgMUvRHKI7WneBdvciVgn2iouUYDH3\nWQJ3eYlPZuA/sdL/awpkTWP3ZfdBmt3xe3/yAOUDaoEdE0sOLP3r6PbW1bsc\njMtsHElq6ODqx/89sV+Eia8tGm9MhaSzGZ8zftRJ5fpi4CAC/mHSGcQrhkyK\nbHpQJDV/yXyfXRPiUSG6znwAuH32TMbIWm6EpeGjecohpuprv2V/T2gFd0GT\nZh8uFDLzEinspS9LW0kS+f3ipGh4D2mzikBsVYktcrOod05DK5MY1TRHw6Bu\nlTXKUxA+98vyksdoOVEXQ89BhMAn6ql8RMC+6vZqNU07ZSWbM7vGxPtTIvQ7\nv+CmU84PXuWcYW97LPb5I2dI1gEaSYC46HeeH5l04RsJPrDSxMihxID5BjhV\n12GszAEgeHxy8eXLo+jWmyc5pxWxi5RWGlvXwxKs1VnBXofitGiEo5exl6ap\nChmfPJBvaW6PTsxUTE1EQAbZ6zhN06jUem6Yk7PRA69na4RJhvts5TRXxYvF\nhdxRFm67VAy1weWAUQxj6h1DDOSLWl5IKjbkGlhqp5bbWJgEuudkvDgcRa6B\n/ZUH\r\n=aKSB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./types/index.d.ts", "engines": {"node": ">= 8.10.0"}, "gitHead": "a4aa5d3ceb29c8e61d61c4c88cd49c79a55a0508", "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --exit", "dtslint": "dtslint types"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.1", "fsevents": "~2.1.1", "readdirp": "~3.2.0", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "eslintConfig": {"env": {"es6": true, "node": true}, "rules": {"radix": "error", "no-var": "error", "quotes": ["error", "single"], "strict": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-spread": "error", "prefer-template": "error", "object-shorthand": "error", "prefer-destructuring": ["error", {"array": false, "object": true}], "array-callback-return": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}]}, "extends": "eslint:recommended", "parserOptions": {"sourceType": "script", "ecmaVersion": 9}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.2.2", "sinon": "^7.5.0", "upath": "^1.2.0", "eslint": "^6.6.0", "rimraf": "^3.0.0", "dtslint": "0.9.9", "sinon-chai": "^3.3.0", "@types/node": "^12"}, "optionalDependencies": {"fsevents": "~2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.2.3_1572225002923_0.8252299995494692", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "chokidar", "version": "3.3.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.3.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}, "dist": {"shasum": "12c0714668c55800f659e262d4962a97faf554a6", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.3.0.tgz", "fileCount": 8, "integrity": "sha512-dGmKLDdT3Gdl7fBUe8XK+gAtGmzy5Fn0XkkWQuYxGIgWVPPse2CxFA5mtrlD0TOHaHjEUqkWNyP1XdHoJES/4A==", "signatures": [{"sig": "MEYCIQD7RSdiOhWkVjMn12ts+1vilEIIrn0tjAio0KhN9vtSwgIhAO/PHFIwHzBYxEe6XfE5E2moVjyBdE0rT+HakOrhRz3M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvUSRCRA9TVsSAnZWagAAt3wP/jVqX+Q2fNDpuYlajcD2\nsVAkMM3PSzNIScyp0yW0zBoysTOUPu7FKPpaSav1O8suNjTO6vkJ57m7DuUm\nkRNDzfemUqUBU+H9p+c0O1IxQgQxwYZDS480/0k/6KAgV0JdFFwW6okCLB3x\nV82Ok7H7gK3++wgC7w9nlywCZ+ZjZNgZsQZhMQngCpyzgbe6YM+qjfeLGbdf\nbILfzVGaOjV0HxsVKS8btKqYFgArS/S/wwdLWOoUb7Upd+om3za9biNUausc\nQchC6WR3m8+25pvvq9Fo+J9SL+8K5GMx/46/ktf8Zf+dNKeYO+hBScu7nJSw\nPRzxqw5kfRQE9eMxBIdchJj4DJKu2hp0x9LVK2OJZ8gJ4xqC2AxcHU/eoxyR\nYIRhjPTwXiQq+22wvxpXQU5UrPdhsSAe+gsOiE4oaAdncl5ybuYdqsz4zIms\nliw281Ns/r4RlW/kThNYaelsXAZPER6bDAQbbMPQtyncUavcKhwB+Oore/yh\nG4Rs6ZawvOSxdg75xXwTLBZ3Lvw1rYMHyH4zhPnDdxZ630/FwBYisCbnXqbD\nH7prirhn9c++bnML99RS/pAYpsHulwZgJ410ufhZKP3+QPkV0ERFKd9HzZB0\n5fLslvkrbccrf2cnip05B5gHOlDUAZavWTdIAQC3YR+exA5OkxD7JgD+m/ng\nSRPu\r\n=DZQl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./types/index.d.ts", "engines": {"node": ">= 8.10.0"}, "gitHead": "ccf759aac9af8a484924aeacd1e1a5280f508a75", "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --exit --timeout 60000", "dtslint": "dtslint types"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.1", "fsevents": "~2.1.1", "readdirp": "~3.2.0", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "eslintConfig": {"env": {"es6": true, "node": true}, "rules": {"radix": "error", "no-var": "error", "quotes": ["error", "single"], "strict": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-lonely-if": "error", "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-spread": "error", "prefer-template": "error", "object-shorthand": "error", "prefer-destructuring": ["error", {"array": false, "object": true}], "array-callback-return": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}]}, "extends": "eslint:recommended", "parserOptions": {"sourceType": "script", "ecmaVersion": 9}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.2.2", "sinon": "^7.5.0", "upath": "^1.2.0", "eslint": "^6.6.0", "rimraf": "^3.0.0", "dtslint": "1.0.2", "sinon-chai": "^3.3.0", "@types/node": "^12"}, "optionalDependencies": {"fsevents": "~2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.3.0_1572684945173_0.606778404802631", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "chokidar", "version": "3.3.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.3.1", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}, "dist": {"shasum": "c84e5b3d18d9a4d77558fef466b1bf16bbeb3450", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.3.1.tgz", "fileCount": 8, "integrity": "sha512-4QYCEWOcK3OJrxwvyyAOxFuhpvOVCYkr33LPfFNBjAD/w3sEzWsp2BUOkI4l9bHvWioAd0rc6NlHUOEaWkTeqg==", "signatures": [{"sig": "MEYCIQCFODSQd3tXmy95i8Ix+tszPiJm9sdBcI2y49r4+yRdtQIhAOHPXc5EV6BvIFMrzti0vuCFr58uax/GzPqhTSOCe2XC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9nwkCRA9TVsSAnZWagAAWecQAJTV/2rFLSfg7hu6hPAJ\n05dq/71D1/SiO/auUV1NUK/txOj3cCEZ+7j5oj9/Qa+GjUPPRbpmY53GGp9G\nFQ1tXPHPganu3XLM8PX+DpsJOJAOHgE/EH5ODLZuOISidHvLJ9DQu/EJsrjo\nTlmC+pIABvJfGDdNFntwq+tES37vsWo9+fnYiJr8GqNeaWHEn4rpqU76MTeB\noYFxzhSRES1pqgqYU9dN9KMoOSFwVVOPTgY4hTgjWYAZfioDfiWHhpK5dpnI\nq4NFoUjTSeQU1smhklpqFqsd9P4M1tvWq+abBCrcb9mSOozjpF/EJmnPSjiH\necFyXBy17nDtZ1zUsJE+4Rubatv+6Xp//45xt0ATjGsJ91mGU+f15pPrztQv\ntQKX+dQ7/FYjzQCmQuO1rgcboCQ2Tzes8Gigr52UTsInggDjZ7sst30jybby\nu51TatZ7eip3MgkGROJuMQj4swJy1xQ+PmLVZ4b+gn3rNuxqSd+qCjR/HC8g\nBRhbNrZk7wLPHQcqlqnWxP/X8zVSykpgkwVbtVulTUV8Nxtcf2TW6ZGe62zr\nV5ifkXvVTW5d1lfjmrY4Pa2N2Gtdkn1IeYFfBWOivjUNPT0uOf5CTFGED80H\nICYQVZK6FtMB65K1r+pcoHjnfeaP1SGEevK4ddRRt6wkyeBgvenwYBWn05tW\nd93I\r\n=pIg2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./types/index.d.ts", "engines": {"node": ">= 8.10.0"}, "gitHead": "1cbbef6bf8d382433011ef295ccfaf52da6038ed", "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --exit --timeout 60000", "dtslint": "dtslint types"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "13.2.0", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.1", "fsevents": "~2.1.2", "readdirp": "~3.3.0", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "eslintConfig": {"env": {"es6": true, "node": true}, "rules": {"radix": "error", "no-var": "error", "quotes": ["error", "single"], "strict": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-lonely-if": "error", "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-spread": "error", "prefer-template": "error", "object-shorthand": "error", "prefer-destructuring": ["error", {"array": false, "object": true}], "array-callback-return": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}]}, "extends": "eslint:recommended", "parserOptions": {"sourceType": "script", "ecmaVersion": 9}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.2.2", "sinon": "^7.5.0", "upath": "^1.2.0", "eslint": "^6.6.0", "rimraf": "^3.0.0", "dtslint": "^2.0.0", "sinon-chai": "^3.3.0", "@types/node": "^12"}, "optionalDependencies": {"fsevents": "~2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.3.1_1576434724275_0.9223256192495166", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "chokidar", "version": "3.4.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.4.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}, "dist": {"shasum": "b30611423ce376357c765b9b8f904b9fba3c0be8", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.4.0.tgz", "fileCount": 8, "integrity": "sha512-aXAaho2VJtisB/1fg1+3nlLJqGOuewTzQpd/Tz0yTg2R0e4IGtshYvtjowyEumcBv2z+y4+kc75Mz7j5xJskcQ==", "signatures": [{"sig": "MEUCIANlQjaGECrJyzHJp0wwOlKYes74K2f44ZOuxjFUsu4fAiEA3d6ZeOXE5R4WcTu8QOiUyt7hNI7aM+Scnn0JtC3b7Gw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepZipCRA9TVsSAnZWagAARTIP/jqI9SO0/hOgybHfCGam\n7u3Ip+brHiO3ix/y8F7+xKbDg6/3NNmZSyxs23s1djuuuETQK7X5xPQQZaH+\nNdpbltN7OO6NkkFAkz9weZsRgWmABTlmhBibnwzXOR9WW7Ka1lTFPH3NmQ/W\nmAO2cytZkFYYxT2UlHKPUMxmv8fs97cNSDAXCtZJIcCLV0MOj1sk3VcMEuhp\nBokExObz9QTZ3LMiWLVyTyThtaD5t3VomK3HfzS31mweN7VMoz+hf0yKzITI\nnneAiRGzR0dsCtnt60aiYf3Dlc3sd05j1GOna141Q9yo6rbFMFOrVRJiBCVU\nRiJlMC/1y2eHyTnQUHOJd35kx74ugZSkr5xPDUoDETD9+8G9tMZyn/W2RmNh\nT/EkWJFGXZUoUO3Y02ASYy431Gz4G5q3e/UAFjBAebZA30Wr81iGLPlPolvo\nPuhm4NKxO5s02X9HAXxnvaG4sgMD10QyPEhC7h/2r2mX4Fr0utlBJrgI131E\nHCuMEUzl/wEdznxzLG7eflP0mQYzARbdTY3EvS4dhYFawdYiDP8Jjhzv+YD/\nMkLON6I6kGpwU4A3/kSyKyG9+mQDQRYIsZdjqa7wRaTos+SrNpiP/653wRjw\nrl7vW4m5tAii+0Jd0zoRqLwRvdCNYGIasRdtlNdUGePTF+Rld4tI9FnjMcDr\n37Gp\r\n=dLb+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./types/index.d.ts", "engines": {"node": ">= 8.10.0"}, "gitHead": "b67d208fbd7314dd77135adbb84e2b9c509f4345", "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --exit --timeout 60000", "dtslint": "dtslint types"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.1", "fsevents": "~2.1.2", "readdirp": "~3.4.0", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "eslintConfig": {"env": {"es6": true, "node": true}, "rules": {"radix": "error", "no-var": "error", "quotes": ["error", "single"], "strict": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-lonely-if": "error", "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-spread": "error", "prefer-template": "error", "object-shorthand": "error", "prefer-destructuring": ["error", {"array": false, "object": true}], "array-callback-return": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}]}, "extends": "eslint:recommended", "parserOptions": {"sourceType": "script", "ecmaVersion": 9}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2", "mocha": "^7.0.0", "sinon": "^9.0.1", "upath": "^1.2.0", "eslint": "^6.6.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "sinon-chai": "^3.3.0", "@types/node": "^13"}, "optionalDependencies": {"fsevents": "~2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.4.0_1587910824726_0.9641785913377525", "host": "s3://npm-registry-packages"}}, "3.4.1": {"name": "chokidar", "version": "3.4.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.4.1", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}, "dist": {"shasum": "e905bdecf10eaa0a0b1db0c664481cc4cbc22ba1", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.4.1.tgz", "fileCount": 8, "integrity": "sha512-TQTJyr2stihpC4Sya9hs2Xh+O2wf+igjL36Y75xx2WdHuiICcn/XJza46Jwt0eT5hVpQOzo3FpY3cj3RVYLX0g==", "signatures": [{"sig": "MEQCIFP52cLJiVVP5MSP1YNRFlReZUOFoq5Zzs7mjPryGIq1AiAhvNmLWVt/OlwO9JQ7eWc4NfeaztYXzYat2TP4TnNmEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfD7rqCRA9TVsSAnZWagAA5y8P/0BD0rCQWyBHanh59Ec0\nPkD7VK5jH0FhG+ZmKUQ3zH1sIM8keLQ7UWxXr2Y8iJfHJ8XB0FCp5D/XHEyo\nsKAEqVHo4PzH8dHvIR+KcyAuEJCtd7sW8Ncf94hsHbs9V8VjhVJyxVD4m0IW\nK/+Z91rJmjU4mPqHOu3vKLernkxBFVITE3tMoiJhlHsf5w0maEHNBVQ6RTG5\nY7/j6TV2VfwqcR5jnglRJiHcr+BVftNCLZoA3HCUOr00wCW5DvnfGoPdCrin\nGESAfR6ppRUSDwTLVDDPVMizBx+bzDKeKCKxf0HehHOJtwGy97m13rI9FphO\n1Q19bcH/vse0KFJFnPVg+SupCs7sKxwzJJfb9Zjq/REgz5BRxtjrBLGfaQuU\nCWWl44qBDphsFA7s6nAWttC4COIAhGFfRMywK8jtz4R96yAE8x6QHOV/Jj3x\nKFf/sB3n1uALUrUuXfm3cC02d2IXXd4bBQRYIaWQigAgiLxfjDVrsgQnZQCI\np5Z0JeIkvknaMgJHBgeukhTgG0WrFT3gmKls9m4dJgmE8td9OYPjjo8owWkW\n7qgnAf5Y+6Rt3EXYz3eolDUg5GBzAClCuqOp/bm/so7zeqXmsiM2H7Lg4EV2\nLkzi2gk73dKwJlDUuiDIl25V5zouic9KNbxFIzNiEppf/4vZimu6VFqFPu2S\nErkb\r\n=cXpg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./types/index.d.ts", "engines": {"node": ">= 8.10.0"}, "gitHead": "06c74cadcb29aeed4f146776c44dc99dc98fd80d", "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --exit --timeout 60000", "dtslint": "dtslint types"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "14.2.0", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.1", "fsevents": "~2.1.2", "readdirp": "~3.4.0", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "eslintConfig": {"env": {"es6": true, "node": true}, "rules": {"radix": "error", "no-var": "error", "quotes": ["error", "single"], "strict": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-lonely-if": "error", "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-spread": "error", "prefer-template": "error", "object-shorthand": "error", "prefer-destructuring": ["error", {"array": false, "object": true}], "array-callback-return": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}]}, "extends": "eslint:recommended", "parserOptions": {"sourceType": "script", "ecmaVersion": 9}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2", "mocha": "^7.0.0", "sinon": "^9.0.1", "upath": "^1.2.0", "eslint": "^7.0.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "sinon-chai": "^3.3.0", "@types/node": "^14"}, "optionalDependencies": {"fsevents": "~2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.4.1_1594866409709_0.2594698973354652", "host": "s3://npm-registry-packages"}}, "3.4.2": {"name": "chokidar", "version": "3.4.2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.4.2", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}, "dist": {"shasum": "38dc8e658dec3809741eb3ef7bb0a47fe424232d", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.4.2.tgz", "fileCount": 8, "integrity": "sha512-IZHaDeBeI+sZJRX7lGcXsdzgvZqKv6sECqsbErJA4mHWfpRrD8B97kSFN4cQz6nGBGiuFia1MKR4d6c1o8Cv7A==", "signatures": [{"sig": "MEUCIAtedDbzfg/nA4v0IKKsEOnqTnM3/NkwMjLC7YN9meXSAiEA8EPcVc14IdSAWjZfjufRfaFbKhh7qEvnUSBd7rh58Ao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89867, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLCYtCRA9TVsSAnZWagAAnNEP+wX0gz3UVU2cq2tykVXm\nFPawXovuSHqgBm2jybmWcjpc5K1TVPnbKcpPdfZWV4ihAntzwq8uDT0BNEbW\ns+8qUDsXKYMX+gzDSn3RIfidAydwLrC4WcjDO1zgDgm1CI3JWxyj8pcaigym\nr8wNg/EDZ2W6tqmRP/CpwaB7zJge+OQs7l2vgkVd8cR3/r9N2/EVEooDyKrv\nT+5i5ifeePDjQFe2kb5h4aVDf+8jslQr5rVVDFHoXUXdUiWLizdVuc3SyHbH\nAvBmk3baSWgSMojjqiysWoSBQcje+cBQ8ZpthMaHDv5FH+w7OBJOTWsLw83S\nq9D9GyKqaZl4LWjnpIBQQO5PPt3WxCCPyV0zZ2IYhLE1d7MKCBBwLCHw1yAx\nt+91eCnVEDxIf6NcwVCZJs1LGXTnJnjYEsCWTZuJPyo0BfHOr+yVAafIB+JO\nmbUl2m6o9epOEH2m3BL4m14ZK61hIaT05+fLQzYpVWxBxj3gpiJARetTvnc5\nUkAD9rFahZqB1ZwpHIjCevDxD+2ZOF+r5fVdCruY2UEmAa5SmKAMYXyH63qC\nhKeFUCMm6xiljuJuDfNnQZxQSohEyoMj6jvbmW8GsQiNqb//mzXIiKKAdFBJ\nySmCS8afDkYaN+PsOYcGPmfEMK5FmSZqo0VeSk7udOM2dC3ph77/v7wAqsXo\nrPQc\r\n=nZ80\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./types/index.d.ts", "engines": {"node": ">= 8.10.0"}, "gitHead": "95faa2fe42d1736b3c5bfbc0f17a9f0379b001f3", "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --exit --timeout 60000", "dtslint": "dtslint types"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "14.2.0", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.1", "fsevents": "~2.1.2", "readdirp": "~3.4.0", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "eslintConfig": {"env": {"es6": true, "node": true}, "rules": {"radix": "error", "no-var": "error", "quotes": ["error", "single"], "strict": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-lonely-if": "error", "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-spread": "error", "prefer-template": "error", "object-shorthand": "error", "prefer-destructuring": ["error", {"array": false, "object": true}], "array-callback-return": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}]}, "extends": "eslint:recommended", "parserOptions": {"sourceType": "script", "ecmaVersion": 9}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2", "mocha": "^7.0.0", "sinon": "^9.0.1", "upath": "^1.2.0", "eslint": "^7.0.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "sinon-chai": "^3.3.0", "@types/node": "^14"}, "optionalDependencies": {"fsevents": "~2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.4.2_1596728876948_0.07557304314268287", "host": "s3://npm-registry-packages"}}, "3.4.3": {"name": "chokidar", "version": "3.4.3", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.4.3", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}, "dist": {"shasum": "c1df38231448e45ca4ac588e6c79573ba6a57d5b", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.4.3.tgz", "fileCount": 8, "integrity": "sha512-DtM3g7juCXQxFVSNPNByEC2+NImtBuxQQvWlHunpJIS5Ocr0lG306cC7FCi7cEA0fzmybPUIl4txBIobk1gGOQ==", "signatures": [{"sig": "MEUCIQDG48YtMr3rx1JI6wDuY3KtwU9PJOsWIRTFCl0+WDNrtAIgerstmHRWnRoIKxELeKap7PfBoNHr3XB9QGXd4QZlww8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89867, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhYnaCRA9TVsSAnZWagAA5VYP/iJbpsYyBRAOv3NSWWfo\n1QpZHY8zNpzq45VS3v8YYrStHeWcV0GprKxItXrAUmWFWv4SAkSuAq9lb9oQ\nbUyDjPwfkoO+qGr87Ewvg0Y57utVjua7mdiLDe8upBfVGTQz9QohczPC35BV\neqnPVGLsZusz6Pp7vGEuRK32K1u3FfZ9HObpy12AEeL7Fj6LM6cofEvkQbDs\nNKZY9jUebAx2+eMsLPlAY6XC1FFrn8j0E8T6x90PDBpJ9O0fU0OYsgQe2F19\nYdnts/Hstw6B925UhOeAWsmyW3exC8lkf6kLqOd2bfvqaTdjRyBaGqBIY0QD\nkpS9Q4JUE/V7R3sk+tPoZCIS/Td9kvpI6IQ0gD7KMYwjZD4y8gZnYvNTRe0I\nnEKWxTs9SP0esA5KZS+tKrqrorjhNGx+36jSrz6uw2NRpSYczTEKjEa7MqQk\nv6FybuZEv6sbITIqy/4hfunwD32TnNcjsWirFb3g+6/81LFib4TSLqNIKSw8\n6wAjwtgLbug3l8esoVStIxskndyhv1dL9Cqtqd5/09XfQmti4iqL2LHTsJq7\nyEwSqqmBpjpI2zxGWgzw5KuKUn0O0QNq65oWTAFRZMCzgNQGu7qh0EQruyGK\ndta4I6LMIgGfRlcixYfie6cBSQJ9SSup5joNa6S7jW4qTeoSNw50NBV6Uh9Q\nCM7v\r\n=Ik5h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./types/index.d.ts", "engines": {"node": ">= 8.10.0"}, "gitHead": "0d7287341c579a8b5f550e7c5350cd2f480db5ae", "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --exit --timeout 60000", "dtslint": "dtslint types"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A neat wrapper around node.js fs.watch / fs.watchFile / fsevents.", "directories": {}, "_nodeVersion": "14.13.1", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.1", "fsevents": "~2.1.2", "readdirp": "~3.5.0", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "eslintConfig": {"env": {"es6": true, "node": true}, "rules": {"radix": "error", "no-var": "error", "quotes": ["error", "single"], "strict": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-lonely-if": "error", "prefer-const": ["error", {"ignoreReadBeforeAssign": true}], "prefer-spread": "error", "prefer-template": "error", "object-shorthand": "error", "prefer-destructuring": ["error", {"array": false, "object": true}], "array-callback-return": "error", "prefer-arrow-callback": ["error", {"allowNamedFunctions": true}]}, "extends": "eslint:recommended", "parserOptions": {"sourceType": "script", "ecmaVersion": 9}}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2", "mocha": "^7.0.0", "sinon": "^9.0.1", "upath": "^1.2.0", "eslint": "^7.0.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "sinon-chai": "^3.3.0", "@types/node": "^14"}, "optionalDependencies": {"fsevents": "~2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.4.3_1602587098206_0.2543214523481734", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "chokidar", "version": "3.5.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.5.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}, "dist": {"shasum": "458a4816a415e9d3b3caa4faec2b96a6935a9e65", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.5.0.tgz", "fileCount": 8, "integrity": "sha512-JgQM9JS92ZbFR4P90EvmzNpSGhpPBGBSj10PILeDyYFwp4h2/D9OM03wsJ4zW1fEp4ka2DGrnUeD7FuvQ2aZ2Q==", "signatures": [{"sig": "MEUCIEUyKCh+O4StWyPOhylNpxco9kDwMiApRP0+29FxouDWAiEA/HeusCYHochFJfpJGUvwUBHGQwlDU32O6MRsc69lQ+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9WfyCRA9TVsSAnZWagAAuqMQAJJUBcLq++dZQ1VLBme+\niizejgL3DGdCtxQHutd84PujiqmukCLP1FnKVI97xEPigTm2oTyhqNjXWI33\ntY6QMB4z7HrDbdHCbESVIJI5+6v4j/r01ojY4lrkvgJhDQS3IvZnfjtMvplT\nh1iDJGgf/ggGWWKIIdUA9PSqkpziYK8Y9mLaIzuAC6mTb1Gk0eCSHwuyc8t+\nt9pqmpmnH+hQdgu+K5KGlGQ9HnzhfLv8pboNow+vbtvSsLHVcbdFpGXgsVnZ\ndt2F5+drlnN14y+lcF8TwDxFYbMbngYsu/UVsKSEkgFpkcrii1Vhi4S16lCF\nIPELn3Ozu0tenKmWZ6Zb+UNNIe/DsEQwkN+URs/Cqp5F17pqYQOliDZKYvJM\nUSrv7v4mZYQwg9ciaoY7YVdn5dlrTNMnmF/xS8H9sQsM9s1s6iWp06UuF0QB\nbYOEb1RMLGdZEFekIQfdwE2DMNIQiGItkLHWv9O5/TXlnuXtbx8UziGREZdS\niWW0rlHGAMvSE70ynE4u6Hlh2XWGEYrqicnqXDnaOWlCMq4uBmv5E7oE6F+f\nFXnLz5iLGz0SDkR+zJc20gTZi+3WJdSWQVjcoM9YhwRsCMZvwWIIi/tH4MwR\nBOrASJGilTkrRBued/yKUlaFJbDOho4lfNHzJeJm6zZBryp1CsxAoVc0Rhwe\nV30B\r\n=iBUe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./types/index.d.ts", "engines": {"node": ">= 8.10.0"}, "gitHead": "b8b3639d52b69ee001d22e485ccb31232dfeac90", "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --exit --timeout 60000", "dtslint": "dtslint types"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Minimal and efficient cross-platform file watching library", "directories": {}, "_nodeVersion": "15.5.0", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.1", "fsevents": "~2.3.1", "readdirp": "~3.5.0", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2", "mocha": "^7.0.0", "sinon": "^9.0.1", "upath": "^1.2.0", "eslint": "^7.0.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "sinon-chai": "^3.3.0", "@types/node": "^14"}, "optionalDependencies": {"fsevents": "~2.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.5.0_1609918449477_0.9590209574819353", "host": "s3://npm-registry-packages"}}, "3.5.1": {"name": "chokidar", "version": "3.5.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.5.1", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}, "dist": {"shasum": "ee9ce7bbebd2b79f49f304799d5468e31e14e68a", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.5.1.tgz", "fileCount": 8, "integrity": "sha512-9+s+Od+W0VJJzawDma/gvBNQqkTiqYTWLuZoyAsivsI4AaWTCzHG06/TMjsf1cYe9Cb97UCEhjz7HvnPk2p/tw==", "signatures": [{"sig": "MEUCIF+U9+0pxnlNDJOjsxTbZ4quC1dgMlhnIIv7nYNgh0J0AiEAzDehLd7aGxHyOE4XYzXK9FcF/aid+aGrDA67QPGwiJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAagFCRA9TVsSAnZWagAAVKcP/2kF/cAPErFEC8PYF3ml\nmEBCgWBtl6eWTgsrIC2k/cVMKJ+EWjcjRKkr3jCwyY2DVy45M455S9BjV1Tu\nfMz/9cvU67w/Mi4l6aQKrHTxsC1HKNCEADiAXWz0E4gTAHXNEEJkPn/GJjrx\nvq14oX8dcs8jkL5wqtR6tc+0iiQoxJUA5X5JoPEFtTtMK8pmU4YF4FT9Vphl\nX58nDOfkNTmJYujbzjtqN+oEdhZNZpEUXNCDJgFv0ZPiqwP41fVIrTPkD/op\nzZk1+3rwvpq9HHHSxupHbyPIsnK93UchE5yay7Pg+FwAQ/OhmwS4D+OXP/ED\nLg9SHn62KfE1+HPQjUBr6GpYOCcjCuuiHuOxnMzv0i4YvdwyGUjYAZ9SMxCv\nvCLIO8wl4f687NDAbE4Z2NmhrH9ogFc9UPhBpbwbLH/l5oMYUL9RI75eiqhP\nVwHSX1BQYGXmEYy6jnVrgTkHMnNUwZpFA9PTGtDRraxHCasfkroly/G9uR8+\nQv1tXudS6QHwuMYYHX0s8OoGYDZSfEsjDTH2lLHgPy9RLPUr+j2apStNN9fh\n8QSJcq084wSlq+ty+Q86tZMXR9+F6EeiJ6GbP7BKwRL+TXDab4BxGbf55d0f\nAHEJKzokFhYurc0mnSUJT5wA6BQOBVbO6O9bwKfuuXm4itHGoaylb9jbkjkn\ne/36\r\n=9Plo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./types/index.d.ts", "engines": {"node": ">= 8.10.0"}, "gitHead": "1d226cbfa50292e122043ceb3c1f4c34a5acd16e", "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --exit --timeout 60000", "dtslint": "dtslint types"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "Minimal and efficient cross-platform file watching library", "directories": {}, "_nodeVersion": "15.5.1", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.1", "fsevents": "~2.3.1", "readdirp": "~3.5.0", "glob-parent": "~5.1.0", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2", "mocha": "^7.0.0", "sinon": "^9.0.1", "upath": "^1.2.0", "eslint": "^7.0.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "sinon-chai": "^3.3.0", "@types/node": "^14"}, "optionalDependencies": {"fsevents": "~2.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.5.1_1610721285165_0.4549628863379902", "host": "s3://npm-registry-packages"}}, "3.5.2": {"name": "chokidar", "version": "3.5.2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.5.2", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}, "dist": {"shasum": "dba3976fcadb016f66fd365021d91600d01c1e75", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.5.2.tgz", "fileCount": 8, "integrity": "sha512-ekGhOnNVPgT77r4K/U3GDhu+FQ2S8TnK/s2KbIGXi0SZWuwkZ2QNyfWdZW+TVfn84DpEP7rLeCt2UI6bJ8GwbQ==", "signatures": [{"sig": "MEYCIQD4vEiyabvaKZZZHUPSznQiwr1EQkp4yHwbuBC99CElqwIhAJZWGxGQEZgyAc2xWeKlqYUL+nwbWKQ7yXUQqqIwUuBS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyKn9CRA9TVsSAnZWagAAUwcQAIUfyWC8mQLkWx9EQrl3\ntGV6DBUQHwvPm/QkNjTMDiP0U2honUnUYowi5HNjNyv62/RjBMihXuAb5pk7\nlLRznC0lAoQlHBcfEaUcWYFQDnsK5St8ZY8LE/ZbqX0mQJP7m+eq9ek4l+zj\n4ejakoc810CL+mBp4rd20hF33JpGrSAea0Qf9JhvKaIq1523I4aPZltmsgWu\nOeWMBGObANOv58SiBRAWoWtw8QNYC3dH3Lm11KKKd1pkWuQGABmzOOjZquLt\nk1Ga0RPy7QnfjXng+6qCT9LwC1jUr6XTz10nadWaJjd/DTULRFsTDICGPXUG\n1s8f74eT56lDW4Tf1r07Vz/SRuYEwQ9v8zjbX58Cruu0w4n1Vw7a2fDJKesX\nsY7gzeBveTGt7cTsFshrdjQWBRKKYgps7Pk9HR8kyA16NsuBDD0XsScOUhQ7\nP/Vc1XcCltrcnKSf9bGmOydyyZ9NOY23ivlVeQcqYieBHm3sNPFkoz+avMZU\nm4h5NGuBxY4+eMcmvkCMX7p4WrPqlNQDeCWVON5JscAt9BZOqz+kZIpExw6m\n98YGL0JBvhdkmDEdN+L2vudv4HWzGyrJ2F338LbPRfJ3hOdn43fToMGEHfS1\nHRrDj06LjfNTO66go2h7nLwR/TgyxFE8JAVQzgEfQIVXyXbHNOUekycQ86IN\no42a\r\n=6hYp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./types/index.d.ts", "engines": {"node": ">= 8.10.0"}, "gitHead": "aa49e9e00fda1e663a6853492c46de8f8008e773", "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --exit --timeout 90000", "dtslint": "dtslint types"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "7.16.0", "description": "Minimal and efficient cross-platform file watching library", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.2", "fsevents": "~2.3.2", "readdirp": "~3.6.0", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.3", "mocha": "^7.0.0", "sinon": "^9.0.1", "upath": "^1.2.0", "eslint": "^7.0.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "sinon-chai": "^3.3.0", "@types/node": "^14"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.5.2_1623763453104_0.8201007788762233", "host": "s3://npm-registry-packages"}}, "3.5.3": {"name": "chokidar", "version": "3.5.3", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.5.3", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "nyc": {"include": ["index.js", "lib/*.js"], "reporter": ["html", "text"]}, "dist": {"shasum": "1cf37c8707b932bd1af1ae22c0432e2acd1903bd", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz", "fileCount": 8, "integrity": "sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==", "signatures": [{"sig": "MEQCIBqrpMd48PaVgpiPiccV8ZZER9JTLuAFkTTMXVA+3+QVAiBn1fNB/S2cZhsfStE3FfuGB3h2+Ba+axG1d+LwT9sPPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5m9bCRA9TVsSAnZWagAAztcQAIDHyIl6hzpIDyV0qrhG\n+z/l/zKpe8R73nh8apUd41z29D7aDUj1piRXt+CaCfLfrRCrmflHN52v2JIM\nv4BV8gh4dMmAvXbCIHpJdjdOzfsqt2Qc9Aabg+HaFBcF8Psc9LlWKHFeaRPU\n2YnZeDjSxHaP2mUCAFkww67wSU60yhjD6zTm61MCt7zezjJSTV3d6HFLE2CN\n8SzY80PoKFjPxyXyYNcP/4XTsWNq2h6jmc1jMqgWqkbYzq9Au7luWmxGnYvq\n1qsIPbDeByu6+UqshsIsQLs2poJIjrdJt2QJK+WxOrtFg1m1ka4Ie4T6jAEc\nxnzWQLe4Nv3On6EXAT2oVRuCLZEGuT2AQKUOMYjLdQudJK6RStuqcgWY8qEm\n2M0kiaa877nEZkehibI0Z0PE0kLVpxrVB82QeHFV1eVOURHAl/SpFw69LBpL\n65zkrMTXY+Ox99JQtXjxAFIhE7Hl5WaVwhw6xCzSsDsQ7SQtFAqsSsNZ2ZQp\nzNifKP7J9Dbm+GWIdtAq6+LOfuWr/D6jX/I2JY2bcRCOHSPgCDYxE8jaZ8Qc\nw6DCFOCkXXgFFNtO3IaeGIHEV7NPEqqEKLU2ZCd5NZZBpVXrnbISBGIOrAW5\nG+fdggxnFb1Xst3+f8/6ZpP6Se6yFgyEoXGo8GvBBZiarAYYLYvY2WJeA8pu\nn2iN\r\n=BB/X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "types": "./types/index.d.ts", "engines": {"node": ">= 8.10.0"}, "funding": [{"url": "https://paulmillr.com/funding/", "type": "individual"}], "gitHead": "4804aba7377baa3e1e22265619c06d374b167fcf", "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "mocha": "mocha --exit --timeout 90000", "dtslint": "dtslint types"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Minimal and efficient cross-platform file watching library", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.2", "fsevents": "~2.3.2", "readdirp": "~3.6.0", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.3", "mocha": "^7.0.0", "sinon": "^9.0.1", "upath": "^1.2.0", "eslint": "^7.0.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "sinon-chai": "^3.3.0", "typescript": "~4.4.3", "@types/node": "^14"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.5.3_1642491739102_0.7172151905657957", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "chokidar", "version": "3.6.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@3.6.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://paulmillr.com", "name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "197c6cc669ef2a8dc5e7b4d97ee4e092c3eb0d5b", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz", "fileCount": 8, "integrity": "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==", "signatures": [{"sig": "MEUCIQDrLz+QQsWUqY9288Mi4YOnMuwPeEYbxzyCC5yi3TMZRwIgDyU1ifjk7JLypXSonu2jzdODCLTzZX7mFb9zTmJvubI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/chokidar@3.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 90200}, "main": "index.js", "types": "./types/index.d.ts", "engines": {"node": ">= 8.10.0"}, "funding": "https://paulmillr.com/funding/", "gitHead": "7c50e25d10a497ce4409f6e52eb630f0d7647b97", "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "test": "npm run lint && npm run mocha", "build": "npm ls", "mocha": "mocha --exit --timeout 90000", "dtslint": "dtslint types"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "10.4.0", "description": "Minimal and efficient cross-platform file watching library", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"braces": "~3.0.2", "is-glob": "~4.0.1", "anymatch": "~3.1.2", "readdirp": "~3.6.0", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "normalize-path": "~3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3", "mocha": "^7.0.0", "sinon": "^9.0.1", "upath": "^1.2.0", "eslint": "^7.0.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "sinon-chai": "^3.3.0", "typescript": "^4.4.3", "@types/node": "^14"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_3.6.0_1707260253958_0.5484643602011041", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "chokidar", "version": "4.0.0", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@4.0.0", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "4d603963e5dd762dc5c7bb1cb5664e53a3002225", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-4.0.0.tgz", "fileCount": 20, "integrity": "sha512-mxIojEAQcuEvT/lyXq+jf/3cO/KoA6z4CeNDGGevTybECPOMFCnQy3OPahluUkbqgPNGw5Bi78UC7Po6Lhy+NA==", "signatures": [{"sig": "MEQCIHgw5wCXd8S8e8ncsAUkqsgCIiuCr43nmsNdy0/GGt4AAiBOA6dLmfZH14xm+owunXTU2r4qcfSzMIXJjE6WncjOkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/chokidar@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 256334}, "main": "./index.js", "types": "./index.d.ts", "module": "./esm/index.js", "engines": {"node": ">= 14.16.0"}, "exports": {".": {"import": "./esm/index.js", "require": "./index.js"}, "./handler.js": {"import": "./esm/handler.js", "require": "./handler.js"}}, "funding": "https://paulmillr.com/funding/", "gitHead": "ac8210f9c9693445935be79900ef5eaf600cc0ee", "scripts": {"lint": "prettier --check src", "test": "node --test", "build": "tsc && tsc -p tsconfig.esm.json", "format": "prettier --write src"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Minimal and efficient cross-platform file watching library", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"readdirp": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.4", "sinon": "12.0.1", "upath": "2.0.1", "rimraf": "5.0.5", "prettier": "3.1.1", "sinon-chai": "3.7.0", "typescript": "5.5.2", "@types/node": "20.14.8", "@paulmillr/jsbt": "0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_4.0.0_1726187416519_0.06845446630126695", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "chokidar", "version": "4.0.1", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@4.0.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "4a6dff66798fb0f72a94f616abbd7e1a19f31d41", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-4.0.1.tgz", "fileCount": 20, "integrity": "sha512-n8enUVCED/KVRQlab1hr3MVpcVMvxtZjmEa956u+4YijlmQED223XMSYj2tLuKvr4jcCTzNNMpQDUer72MMmzA==", "signatures": [{"sig": "MEUCIQDqB4H3gAgsxWdlgdaRKerqdxCaqZAntR/+tt5BfS7KWAIgE0XEBrp94RGwD5FuFJIl8itdHUCUK6VfMLlfeDdxj1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/chokidar@4.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 257054}, "main": "./index.js", "types": "./index.d.ts", "module": "./esm/index.js", "engines": {"node": ">= 14.16.0"}, "exports": {".": {"import": "./esm/index.js", "require": "./index.js"}, "./handler.js": {"import": "./esm/handler.js", "require": "./handler.js"}}, "funding": "https://paulmillr.com/funding/", "gitHead": "8c3479f8c68fc787163bc01283e77b05e8a84ef6", "scripts": {"lint": "prettier --check src", "test": "node --test", "build": "tsc && tsc -p tsconfig.esm.json", "format": "prettier --write src"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Minimal and efficient cross-platform file watching library", "directories": {}, "sideEffects": false, "_nodeVersion": "20.17.0", "dependencies": {"readdirp": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.4", "sinon": "12.0.1", "upath": "2.0.1", "rimraf": "5.0.5", "prettier": "3.1.1", "sinon-chai": "3.7.0", "typescript": "5.5.2", "@types/node": "20.14.8", "@paulmillr/jsbt": "0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_4.0.1_1727010617152_0.9613447687324121", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "chokidar", "version": "4.0.2", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "author": {"url": "https://paulmillr.com", "name": "<PERSON>"}, "license": "MIT", "_id": "chokidar@4.0.2", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/paulmillr/chokidar", "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "dist": {"shasum": "97b9562c9f59de559177f069eadf5dcc67d24798", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-4.0.2.tgz", "fileCount": 12, "integrity": "sha512-/b57FK+bblSU+dfewfFe0rT1YjVDfOmeLQwCAuC+vwvgLkXboATqqmy+Ipux6JrF6L5joe5CBnFOw+gLWH6yKg==", "signatures": [{"sig": "MEYCIQCXb2E4SC1KywYUnfmcZMh7ll5ZKuv/pkhIs4iZ3iXC8gIhAOUBw9GMVnKkHPe6n5M9Fi4aFxur6Fx4jQPICRy4Xkg+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148603}, "main": "./index.js", "types": "./index.d.ts", "module": "./esm/index.js", "engines": {"node": ">= 14.16.0"}, "exports": {".": {"import": "./esm/index.js", "require": "./index.js"}, "./handler.js": {"import": "./esm/handler.js", "require": "./handler.js"}}, "funding": "https://paulmillr.com/funding/", "gitHead": "69c115a3684a5e72e0ac68aa0a57c204f081132a", "scripts": {"lint": "prettier --check src", "test": "node --test", "build": "tsc && tsc -p tsconfig.esm.json", "format": "prettier --write src"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/paulmillr/chokidar.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "Minimal and efficient cross-platform file watching library", "directories": {}, "sideEffects": false, "_nodeVersion": "20.18.1", "dependencies": {"readdirp": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "4.3.4", "sinon": "12.0.1", "upath": "2.0.1", "rimraf": "5.0.5", "prettier": "3.1.1", "sinon-chai": "3.7.0", "typescript": "5.5.2", "@types/node": "20.14.8", "@paulmillr/jsbt": "0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/chokidar_4.0.2_1734376005760_0.571471273784744", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.3": {"name": "chokidar", "description": "Minimal and efficient cross-platform file watching library", "version": "4.0.3", "homepage": "https://github.com/paulmillr/chokidar", "author": {"name": "<PERSON>", "url": "https://paulmillr.com"}, "main": "./index.js", "module": "./esm/index.js", "types": "./index.d.ts", "exports": {".": {"import": "./esm/index.js", "require": "./index.js"}, "./handler.js": {"import": "./esm/handler.js", "require": "./handler.js"}}, "dependencies": {"readdirp": "^4.0.1"}, "devDependencies": {"@paulmillr/jsbt": "0.2.1", "@types/node": "20.14.8", "chai": "4.3.4", "prettier": "3.1.1", "rimraf": "5.0.5", "sinon": "12.0.1", "sinon-chai": "3.7.0", "typescript": "5.5.2", "upath": "2.0.1"}, "sideEffects": false, "engines": {"node": ">= 14.16.0"}, "repository": {"type": "git", "url": "git+https://github.com/paulmillr/chokidar.git"}, "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "license": "MIT", "scripts": {"build": "tsc && tsc -p tsconfig.esm.json", "lint": "prettier --check src", "format": "prettier --write src", "test": "node --test"}, "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "funding": "https://paulmillr.com/funding/", "_id": "chokidar@4.0.3", "gitHead": "1182965a0d3d884f15db423009262778ddba0ac4", "_nodeVersion": "20.18.1", "_npmVersion": "11.0.0", "dist": {"integrity": "sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==", "shasum": "7be37a4c03c9aee1ecfe862a4a23b2c70c205d30", "tarball": "https://registry.npmjs.org/chokidar/-/chokidar-4.0.3.tgz", "fileCount": 12, "unpackedSize": 148759, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBdNPZNFgCQB0Av+H+o5aEev/7DL+txX2or2RCLYBpsbAiEA/qduexHQP8lKlFX2+jqYF/YFTtKsLT8TIUTLXcsa3Uk="}]}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/chokidar_4.0.3_1734560494275_0.7907735207628055"}, "_hasShrinkwrap": false}}, "time": {"created": "2012-04-26T01:32:10.158Z", "modified": "2024-12-18T22:21:34.619Z", "0.1.1": "2012-04-26T01:32:12.087Z", "0.2.0": "2012-05-04T13:53:24.862Z", "0.2.1": "2012-05-04T14:20:02.520Z", "0.2.2": "2012-05-04T14:24:46.390Z", "0.2.3": "2012-05-12T18:28:30.315Z", "0.2.4": "2012-06-06T21:50:00.820Z", "0.2.5": "2012-06-08T14:00:31.344Z", "0.2.6": "2012-06-08T14:45:47.895Z", "0.3.0": "2012-06-24T09:22:45.869Z", "0.4.0": "2012-07-26T03:42:48.520Z", "0.5.0": "2012-12-09T03:06:45.402Z", "0.5.1": "2013-01-06T09:59:50.533Z", "0.5.2": "2013-01-13T12:01:23.486Z", "0.5.3": "2013-01-13T13:59:58.545Z", "0.6.0": "2013-03-10T19:44:21.996Z", "0.6.1": "2013-03-19T03:31:30.240Z", "0.6.2": "2013-03-19T12:53:23.348Z", "0.6.3": "2013-08-12T09:59:34.042Z", "0.7.0": "2013-10-21T23:04:23.465Z", "0.7.1": "2013-11-18T06:17:45.689Z", "0.8.0": "2013-11-29T18:02:47.105Z", "0.8.1": "2013-12-16T15:48:42.986Z", "0.8.2": "2014-03-25T23:09:58.457Z", "0.8.3": "2014-08-14T06:26:21.882Z", "0.8.4": "2014-08-14T18:05:50.214Z", "0.9.0": "2014-09-24T21:44:10.143Z", "0.10.0": "2014-10-18T05:23:47.896Z", "0.10.1": "2014-10-19T16:21:58.063Z", "0.10.2": "2014-10-23T13:41:44.701Z", "0.10.3": "2014-10-28T19:49:23.928Z", "0.10.4": "2014-11-05T22:02:41.296Z", "0.10.5": "2014-11-06T21:33:59.190Z", "0.10.6": "2014-11-12T22:01:28.664Z", "0.10.7": "2014-11-14T19:02:24.014Z", "0.10.8": "2014-11-14T19:26:01.405Z", "0.10.9": "2014-11-15T13:37:55.237Z", "0.11.0": "2014-11-16T16:16:17.262Z", "0.11.1": "2014-11-19T15:27:20.904Z", "0.12.0": "2014-12-08T16:41:53.273Z", "0.12.1": "2014-12-10T17:08:49.269Z", "0.12.2": "2014-12-13T04:11:39.712Z", "0.12.3": "2014-12-13T13:31:46.261Z", "0.12.4": "2014-12-14T15:50:36.038Z", "0.12.5": "2014-12-17T16:08:26.655Z", "0.12.6": "2015-01-06T14:20:01.972Z", "1.0.0-rc1": "2015-01-21T11:48:45.295Z", "1.0.0-rc1.1": "2015-01-21T12:07:30.361Z", "1.0.0-rc2": "2015-01-21T12:09:34.209Z", "1.0.0-rc3": "2015-01-30T20:03:21.832Z", "1.0.0-rc4": "2015-03-04T19:02:28.497Z", "1.0.0-rc5": "2015-03-30T13:07:40.267Z", "1.0.0-rc6": "2015-04-01T18:49:05.263Z", "1.0.0": "2015-04-07T16:35:44.217Z", "1.0.1": "2015-04-08T21:49:31.539Z", "1.0.2": "2015-05-30T15:56:29.145Z", "1.0.3": "2015-06-04T13:23:40.487Z", "1.0.4": "2015-07-15T14:28:35.972Z", "1.0.5": "2015-07-20T16:27:56.626Z", "1.0.6": "2015-09-18T14:35:59.532Z", "1.1.0": "2015-09-23T19:06:09.095Z", "1.2.0": "2015-10-01T19:57:20.267Z", "1.3.0": "2015-11-18T01:37:19.521Z", "1.4.0": "2015-12-03T20:34:13.598Z", "1.4.1": "2015-12-09T21:59:10.892Z", "1.4.2": "2015-12-30T14:59:31.923Z", "1.4.3": "2016-02-27T01:24:57.639Z", "1.5.0": "2016-05-10T21:03:35.544Z", "1.5.1": "2016-05-20T21:17:29.241Z", "1.5.2": "2016-06-07T21:22:15.813Z", "1.6.0": "2016-06-22T14:43:41.390Z", "1.6.1": "2016-10-14T18:36:38.000Z", "1.7.0": "2017-05-08T18:45:57.365Z", "2.0.0": "2017-12-29T22:34:30.082Z", "2.0.1": "2018-02-08T15:11:00.103Z", "2.0.2": "2018-02-14T16:49:28.280Z", "2.0.3": "2018-03-23T04:29:41.002Z", "2.0.4": "2018-06-18T16:50:58.133Z", "2.1.0": "2019-02-05T19:26:21.443Z", "2.1.1": "2019-02-11T21:47:56.668Z", "2.1.2": "2019-02-18T19:56:12.201Z", "2.1.3": "2019-03-22T10:09:18.195Z", "2.1.4": "2019-03-22T10:12:13.889Z", "2.1.5": "2019-03-22T21:18:23.993Z", "3.0.0": "2019-04-30T16:52:43.801Z", "2.1.6": "2019-05-15T12:36:00.092Z", "3.0.1": "2019-06-02T21:56:36.311Z", "3.0.2": "2019-07-07T00:05:26.645Z", "2.1.7": "2019-08-21T17:38:26.838Z", "2.1.8": "2019-08-21T17:48:25.609Z", "3.1.0": "2019-09-16T19:08:54.033Z", "3.1.1": "2019-09-19T18:29:24.796Z", "3.2.0": "2019-10-01T02:43:24.687Z", "3.2.1": "2019-10-01T15:20:12.765Z", "3.2.2": "2019-10-16T02:42:59.097Z", "3.2.3": "2019-10-28T01:10:03.151Z", "3.3.0": "2019-11-02T08:55:45.322Z", "3.3.1": "2019-12-15T18:32:04.428Z", "3.4.0": "2020-04-26T14:20:24.899Z", "3.4.1": "2020-07-16T02:26:49.851Z", "3.4.2": "2020-08-06T15:47:57.157Z", "3.4.3": "2020-10-13T11:04:58.386Z", "3.5.0": "2021-01-06T07:34:09.793Z", "3.5.1": "2021-01-15T14:34:45.374Z", "3.5.2": "2021-06-15T13:24:13.340Z", "3.5.3": "2022-01-18T07:42:19.335Z", "3.6.0": "2024-02-06T22:57:34.159Z", "4.0.0": "2024-09-13T00:30:16.656Z", "4.0.1": "2024-09-22T13:10:17.466Z", "4.0.2": "2024-12-16T19:06:45.997Z", "4.0.3": "2024-12-18T22:21:34.451Z"}, "bugs": {"url": "https://github.com/paulmillr/chokidar/issues"}, "author": {"name": "<PERSON>", "url": "https://paulmillr.com"}, "license": "MIT", "homepage": "https://github.com/paulmillr/chokidar", "keywords": ["fs", "watch", "watchFile", "watcher", "watching", "file", "fsevents"], "repository": {"type": "git", "url": "git+https://github.com/paulmillr/chokidar.git"}, "description": "Minimal and efficient cross-platform file watching library", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# Chokidar [![Weekly downloads](https://img.shields.io/npm/dw/chokidar.svg)](https://github.com/paulmillr/chokidar)\n\n> Minimal and efficient cross-platform file watching library\n\n## Why?\n\nThere are many reasons to prefer Chokidar to raw fs.watch / fs.watchFile in 2024:\n\n- Events are properly reported\n    - macOS events report filenames\n    - events are not reported twice\n    - changes are reported as add / change / unlink instead of useless `rename`\n- Atomic writes are supported, using `atomic` option\n    - Some file editors use them\n- Chunked writes are supported, using `awaitWriteFinish` option\n    - Large files are commonly written in chunks\n- File / dir filtering is supported\n- Symbolic links are supported\n- Recursive watching is always supported, instead of partial when using raw events\n    - Includes a way to limit recursion depth\n\nChokidar relies on the Node.js core `fs` module, but when using\n`fs.watch` and `fs.watchFile` for watching, it normalizes the events it\nreceives, often checking for truth by getting file stats and/or dir contents.\nThe `fs.watch`-based implementation is the default, which\navoids polling and keeps CPU usage down. Be advised that chokidar will initiate\nwatchers recursively for everything within scope of the paths that have been\nspecified, so be judicious about not wasting system resources by watching much\nmore than needed. For some cases, `fs.watchFile`, which utilizes polling and uses more resources, is used.\n\nMade for [Brunch](https://brunch.io/) in 2012,\nit is now used in [~30 million repositories](https://www.npmjs.com/browse/depended/chokidar) and\nhas proven itself in production environments.\n\n**Sep 2024 update:** v4 is out! It decreases dependency count from 13 to 1, removes\nsupport for globs, adds support for ESM / Common.js modules, and bumps minimum node.js version from v8 to v14.\nCheck out [upgrading](#upgrading).\n\n## Getting started\n\nInstall with npm:\n\n```sh\nnpm install chokidar\n```\n\nUse it in your code:\n\n```javascript\nimport chokidar from 'chokidar';\n\n// One-liner for current directory\nchokidar.watch('.').on('all', (event, path) => {\n  console.log(event, path);\n});\n\n\n// Extended options\n// ----------------\n\n// Initialize watcher.\nconst watcher = chokidar.watch('file, dir, or array', {\n  ignored: (path, stats) => stats?.isFile() && !path.endsWith('.js'), // only watch js files\n  persistent: true\n});\n\n// Something to use when events are received.\nconst log = console.log.bind(console);\n// Add event listeners.\nwatcher\n  .on('add', path => log(`File ${path} has been added`))\n  .on('change', path => log(`File ${path} has been changed`))\n  .on('unlink', path => log(`File ${path} has been removed`));\n\n// More possible events.\nwatcher\n  .on('addDir', path => log(`Directory ${path} has been added`))\n  .on('unlinkDir', path => log(`Directory ${path} has been removed`))\n  .on('error', error => log(`Watcher error: ${error}`))\n  .on('ready', () => log('Initial scan complete. Ready for changes'))\n  .on('raw', (event, path, details) => { // internal\n    log('Raw event info:', event, path, details);\n  });\n\n// 'add', 'addDir' and 'change' events also receive stat() results as second\n// argument when available: https://nodejs.org/api/fs.html#fs_class_fs_stats\nwatcher.on('change', (path, stats) => {\n  if (stats) console.log(`File ${path} changed size to ${stats.size}`);\n});\n\n// Watch new files.\nwatcher.add('new-file');\nwatcher.add(['new-file-2', 'new-file-3']);\n\n// Get list of actual paths being watched on the filesystem\nlet watchedPaths = watcher.getWatched();\n\n// Un-watch some files.\nawait watcher.unwatch('new-file');\n\n// Stop watching. The method is async!\nawait watcher.close().then(() => console.log('closed'));\n\n// Full list of options. See below for descriptions.\n// Do not use this example!\nchokidar.watch('file', {\n  persistent: true,\n\n  // ignore .txt files\n  ignored: (file) => file.endsWith('.txt'),\n  // watch only .txt files\n  // ignored: (file, _stats) => _stats?.isFile() && !file.endsWith('.txt'),\n\n  awaitWriteFinish: true, // emit single event when chunked writes are completed\n  atomic: true, // emit proper events when \"atomic writes\" (mv _tmp file) are used\n\n  // The options also allow specifying custom intervals in ms\n  // awaitWriteFinish: {\n  //   stabilityThreshold: 2000,\n  //   pollInterval: 100\n  // },\n  // atomic: 100,\n\n  interval: 100,\n  binaryInterval: 300,\n\n  cwd: '.',\n  depth: 99,\n\n  followSymlinks: true,\n  ignoreInitial: false,\n  ignorePermissionErrors: false,\n  usePolling: false,\n  alwaysStat: false,\n});\n\n```\n\n`chokidar.watch(paths, [options])`\n\n* `paths` (string or array of strings). Paths to files, dirs to be watched\nrecursively.\n* `options` (object) Options object as defined below:\n\n#### Persistence\n\n* `persistent` (default: `true`). Indicates whether the process\nshould continue to run as long as files are being watched.\n\n#### Path filtering\n\n* `ignored` function, regex, or path. Defines files/paths to be ignored.\nThe whole relative or absolute path is tested, not just filename. If a function with two arguments\nis provided, it gets called twice per path - once with a single argument (the path), second\ntime with two arguments (the path and the\n[`fs.Stats`](https://nodejs.org/api/fs.html#fs_class_fs_stats)\nobject of that path).\n* `ignoreInitial` (default: `false`). If set to `false` then `add`/`addDir` events are also emitted for matching paths while\ninstantiating the watching as chokidar discovers these file paths (before the `ready` event).\n* `followSymlinks` (default: `true`). When `false`, only the\nsymlinks themselves will be watched for changes instead of following\nthe link references and bubbling events through the link's path.\n* `cwd` (no default). The base directory from which watch `paths` are to be\nderived. Paths emitted with events will be relative to this.\n\n#### Performance\n\n* `usePolling` (default: `false`).\nWhether to use fs.watchFile (backed by polling), or fs.watch. If polling\nleads to high CPU utilization, consider setting this to `false`. It is\ntypically necessary to **set this to `true` to successfully watch files over\na network**, and it may be necessary to successfully watch files in other\nnon-standard situations. Setting to `true` explicitly on MacOS overrides the\n`useFsEvents` default. You may also set the CHOKIDAR_USEPOLLING env variable\nto true (1) or false (0) in order to override this option.\n* _Polling-specific settings_ (effective when `usePolling: true`)\n  * `interval` (default: `100`). Interval of file system polling, in milliseconds. You may also\n    set the CHOKIDAR_INTERVAL env variable to override this option.\n  * `binaryInterval` (default: `300`). Interval of file system\n  polling for binary files.\n  ([see list of binary extensions](https://github.com/sindresorhus/binary-extensions/blob/master/binary-extensions.json))\n* `alwaysStat` (default: `false`). If relying upon the\n[`fs.Stats`](https://nodejs.org/api/fs.html#fs_class_fs_stats)\nobject that may get passed with `add`, `addDir`, and `change` events, set\nthis to `true` to ensure it is provided even in cases where it wasn't\nalready available from the underlying watch events.\n* `depth` (default: `undefined`). If set, limits how many levels of\nsubdirectories will be traversed.\n* `awaitWriteFinish` (default: `false`).\nBy default, the `add` event will fire when a file first appears on disk, before\nthe entire file has been written. Furthermore, in some cases some `change`\nevents will be emitted while the file is being written. In some cases,\nespecially when watching for large files there will be a need to wait for the\nwrite operation to finish before responding to a file creation or modification.\nSetting `awaitWriteFinish` to `true` (or a truthy value) will poll file size,\nholding its `add` and `change` events until the size does not change for a\nconfigurable amount of time. The appropriate duration setting is heavily\ndependent on the OS and hardware. For accurate detection this parameter should\nbe relatively high, making file watching much less responsive.\nUse with caution.\n  * *`options.awaitWriteFinish` can be set to an object in order to adjust\n  timing params:*\n  * `awaitWriteFinish.stabilityThreshold` (default: 2000). Amount of time in\n  milliseconds for a file size to remain constant before emitting its event.\n  * `awaitWriteFinish.pollInterval` (default: 100). File size polling interval, in milliseconds.\n\n#### Errors\n\n* `ignorePermissionErrors` (default: `false`). Indicates whether to watch files\nthat don't have read permissions if possible. If watching fails due to `EPERM`\nor `EACCES` with this set to `true`, the errors will be suppressed silently.\n* `atomic` (default: `true` if `useFsEvents` and `usePolling` are `false`).\nAutomatically filters out artifacts that occur when using editors that use\n\"atomic writes\" instead of writing directly to the source file. If a file is\nre-added within 100 ms of being deleted, Chokidar emits a `change` event\nrather than `unlink` then `add`. If the default of 100 ms does not work well\nfor you, you can override it by setting `atomic` to a custom value, in\nmilliseconds.\n\n### Methods & Events\n\n`chokidar.watch()` produces an instance of `FSWatcher`. Methods of `FSWatcher`:\n\n* `.add(path / paths)`: Add files, directories for tracking.\nTakes an array of strings or just one string.\n* `.on(event, callback)`: Listen for an FS event.\nAvailable events: `add`, `addDir`, `change`, `unlink`, `unlinkDir`, `ready`,\n`raw`, `error`.\nAdditionally `all` is available which gets emitted with the underlying event\nname and path for every event other than `ready`, `raw`, and `error`.  `raw` is internal, use it carefully.\n* `.unwatch(path / paths)`: Stop watching files or directories.\nTakes an array of strings or just one string.\n* `.close()`: **async** Removes all listeners from watched files. Asynchronous, returns Promise. Use with `await` to ensure bugs don't happen.\n* `.getWatched()`: Returns an object representing all the paths on the file\nsystem being watched by this `FSWatcher` instance. The object's keys are all the\ndirectories (using absolute paths unless the `cwd` option was used), and the\nvalues are arrays of the names of the items contained in each directory.\n\n### CLI\n\nCheck out third party [chokidar-cli](https://github.com/open-cli-tools/chokidar-cli),\nwhich allows to execute a command on each change, or get a stdio stream of change events.\n\n## Troubleshooting\n\nSometimes, Chokidar runs out of file handles, causing `EMFILE` and `ENOSP` errors:\n\n* `bash: cannot set terminal process group (-1): Inappropriate ioctl for device bash: no job control in this shell`\n* `Error: watch /home/<USER>\n\nThere are two things that can cause it.\n\n1. Exhausted file handles for generic fs operations\n    - Can be solved by using [graceful-fs](https://www.npmjs.com/package/graceful-fs),\n      which can monkey-patch native `fs` module used by chokidar: `let fs = require('fs'); let grfs = require('graceful-fs'); grfs.gracefulify(fs);`\n    - Can also be solved by tuning OS: `echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf && sudo sysctl -p`.\n2. Exhausted file handles for `fs.watch`\n    - Can't seem to be solved by graceful-fs or OS tuning\n    - It's possible to start using `usePolling: true`, which will switch backend to resource-intensive `fs.watchFile`\n\nAll fsevents-related issues (`WARN optional dep failed`, `fsevents is not a constructor`) are solved by upgrading to v4+.\n\n## Changelog\n\n- **v4 (Sep 2024):** remove glob support and bundled fsevents. Decrease dependency count from 13 to 1. Rewrite in typescript. Bumps minimum node.js requirement to v14+\n- **v3 (Apr 2019):** massive CPU & RAM consumption improvements; reduces deps / package size by a factor of 17x and bumps Node.js requirement to v8.16+.\n- **v2 (Dec 2017):** globs are now posix-style-only. Tons of bugfixes.\n- **v1 (Apr 2015):** glob support, symlink support, tons of bugfixes. Node 0.8+ is supported\n- **v0.1 (Apr 2012):** Initial release, extracted from [Brunch](https://github.com/brunch/brunch/blob/9847a065aea300da99bd0753f90354cde9de1261/src/helpers.coffee#L66)\n\n### Upgrading\n\nIf you've used globs before and want do replicate the functionality with v4:\n\n```js\n// v3\nchok.watch('**/*.js');\nchok.watch(\"./directory/**/*\");\n\n// v4\nchok.watch('.', {\n  ignored: (path, stats) => stats?.isFile() && !path.endsWith('.js'), // only watch js files\n});\nchok.watch('./directory');\n\n// other way\nimport { glob } from 'node:fs/promises';\nconst watcher = watch(await Array.fromAsync(glob('**/*.js')));\n\n// unwatching\n// v3\nchok.unwatch('**/*.js');\n// v4\nchok.unwatch(await glob('**/*.js'));\n```\n\n## Also\n\nWhy was chokidar named this way? What's the meaning behind it?\n\n>Chowkidar is a transliteration of a Hindi word meaning 'watchman, gatekeeper', चौकीदार. This ultimately comes from Sanskrit _ चतुष्क_ (crossway, quadrangle, consisting-of-four). This word is also used in other languages like Urdu as (چوکیدار) which is widely used in Pakistan and India. \n\n## License\n\nMIT (c) Paul Miller (<https://paulmillr.com>), see [LICENSE](LICENSE) file.\n", "readmeFilename": "README.md", "users": {"291296283": true, "ee": true, "viz": true, "bigp": true, "bret": true, "huyz": true, "isao": true, "mrxf": true, "n1kk": true, "tpkn": true, "tztz": true, "abdul": true, "adron": true, "andyd": true, "craql": true, "d-rob": true, "dazld": true, "dralc": true, "lavir": true, "lgh06": true, "panlw": true, "parks": true, "phntm": true, "samar": true, "sopov": true, "timdp": true, "456wyc": true, "ackhub": true, "ajsb85": true, "akarem": true, "asessa": true, "bpatel": true, "caesor": true, "d-band": true, "daizch": true, "evan2x": true, "gaulle": true, "huarse": true, "joakin": true, "kaycee": true, "lonjoy": true, "monjer": true, "ngyuki": true, "nuwaio": true, "ovrmrw": true, "pandao": true, "papiro": true, "rexpan": true, "ridzhi": true, "sloanb": true, "sprjrx": true, "tcrowe": true, "zewish": true, "zoluzo": true, "antanst": true, "asaupup": true, "itonyyo": true, "ivan.sh": true, "jlertle": true, "jnoodle": true, "laoshaw": true, "mbonaci": true, "restuta": true, "robtarr": true, "scamden": true, "sharper": true, "sopepos": true, "springy": true, "szmtcjm": true, "tsxuehu": true, "varedis": true, "vboctor": true, "wgerven": true, "xfloops": true, "xtx1130": true, "abhisekp": true, "anhulife": true, "austinjk": true, "bonashen": true, "cetincem": true, "cool_zjy": true, "draganhr": true, "egasimus": true, "fabioper": true, "hyanghai": true, "koalaylj": true, "krambuhl": true, "liximomo": true, "losymear": true, "luislobo": true, "manxisuo": true, "nickchow": true, "piascikj": true, "qddegtya": true, "rizowski": true, "rochejul": true, "ronin161": true, "semencov": true, "softwind": true, "vamakoda": true, "wisecolt": true, "xiaobing": true, "yashprit": true, "zuojiang": true, "abuelwafa": true, "alejcerro": true, "allen_lyu": true, "antixrist": true, "dunstontc": true, "edwardxyt": true, "erincinci": true, "fgribreau": true, "gochomugo": true, "goliatone": true, "gonzalofj": true, "larrychen": true, "mondalaci": true, "myjustify": true, "ninozhang": true, "salmander": true, "sqrtthree": true, "xudaolong": true, "yanrivera": true, "abdihaikal": true, "ahmetertem": true, "clarenceho": true, "echaouchna": true, "gerst20051": true, "insdevmail": true, "kappuccino": true, "langri-sha": true, "mattbodman": true, "oleg_tsyba": true, "pragmadash": true, "sanketss84": true, "seangenabe": true, "shuoshubao": true, "silverwind": true, "temoto-kun": true, "amirmehmood": true, "davidnyhuis": true, "deneboulton": true, "flumpus-dev": true, "liruwei0109": true, "monsterkodi": true, "sammyteahan": true, "scytalezero": true, "synchronous": true, "wangnan0610": true, "cooperhsiung": true, "dpjayasekara": true, "ivan.marquez": true, "justdomepaul": true, "mightyjongyo": true, "shaomingquan": true, "tommytroylin": true, "uldis.sturms": true, "zacharyjuang": true, "zhenguo.zhao": true, "humantriangle": true, "jian263994241": true, "lassevolkmann": true, "laurentknauss": true, "matthewcallis": true, "pablo.tavarez": true, "polarisjunior": true, "scottfreecode": true, "vivek.vikhere": true, "yinyongcom666": true, "danielbankhead": true, "sergeymakoveev": true, "shanewholloway": true, "icodeforcookies": true, "jonathanredford": true, "marshallformula": true, "jonathanmelville": true, "theoryofnekomata": true}}