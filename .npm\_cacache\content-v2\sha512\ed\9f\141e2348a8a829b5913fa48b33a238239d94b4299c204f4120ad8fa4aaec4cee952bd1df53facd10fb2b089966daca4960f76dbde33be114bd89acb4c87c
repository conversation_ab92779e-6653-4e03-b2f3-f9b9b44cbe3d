{"_id": "makeerror", "_rev": "27-f7cc3dabf87ce1620a6d67dd6a17220b", "name": "makeerror", "description": "A library to make errors.", "dist-tags": {"latest": "1.0.12"}, "versions": {"1.0.0": {"name": "makeerror", "description": "A library to make errors.", "version": "1.0.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-makeerror.git"}, "scripts": {"test": "./node_modules/.bin/expresso"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/makeerror/1.0.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "makeerror@1.0.0", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.18", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "9a2e2cb9669b6a1c0060b325022feb6a375b0873", "tarball": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.0.tgz", "integrity": "sha512-NWIT+887tUPEnvmYQotXHRyHfPSVG+lNcpJcEGpIU1NPSAneDv7i+vyVsEyxVFds/rrtZzd6C3fCJDEr15i0Ew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDY3YZtJbvck8iNvodJ9zSFf+4i0cZrlwtef3ERJAMZHAIgZbYPONUGODRbAPb+ylkKHI+qXQj0Wi5zGpPfi4kqABc="}]}, "directories": {}}, "1.0.1": {"name": "makeerror", "description": "A library to make errors.", "version": "1.0.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/makeerror", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-makeerror.git"}, "scripts": {"test": "./node_modules/.bin/expresso -c"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/makeerror/1.0.1/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "makeerror@1.0.1", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.18", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "80d00385f0135dfe1560e3134250d7384176dffe", "tarball": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.1.tgz", "integrity": "sha512-+ze41zX/qZgIADXk2xOlZBzM7i66R8GjPju57uBJ7IQBrHJBJOCYM5sPykgQyzYKCLgcpDAQJFIDvT+svUxlwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFFfB+GRqUVk5yjfy6syyz4scGsr8ItULcNyLvPqeX/1AiADbpgkFT0LBxHgB+gd28pakCQKjGBOOENisi2xD54rCA=="}]}, "directories": {}}, "1.0.2": {"name": "makeerror", "description": "A library to make errors.", "version": "1.0.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/makeerror", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-makeerror.git"}, "scripts": {"test": "./node_modules/.bin/expresso -c"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/makeerror/1.0.2/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "makeerror@1.0.2", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "f3a9ed4737226e953e7a26e7d7a3231e6797aa43", "tarball": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.2.tgz", "integrity": "sha512-Q2sVrM8p2crkgpPbUxOqJCJ5kcodecQzGMV5/H9olVMUefJuEgkBjvzovxaYe/iOPaaSic+B4hxoBqOq9Ooppg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7zqKaZNK1r4lPjWufAo+fWEgBsJ1h/LA1zMqHONFSjAIhAM5753POGecb5ePec/HYbMeRkZXUk/3iGHswwLQwkh6e"}]}, "maintainers": [{"name": "naitik", "email": "<EMAIL>"}], "directories": {}}, "1.0.3": {"name": "makeerror", "description": "A library to make errors.", "version": "1.0.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/makeerror", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-makeerror.git"}, "scripts": {"test": "./node_modules/.bin/expresso -c"}, "dependencies": {"tmpl": ">= 1.0.0"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/makeerror/1.0.3/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "makeerror@1.0.3", "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "9cc5b2968436b18a57f78ca65619d48ba30f4c85", "tarball": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.3.tgz", "integrity": "sha512-MnM93cd9nz2/FndVJtfMV8uANBLc/M5xNZv7DuJFfRUv40VSNiFwICjlZxAIFosGrBPkjYoV1RJTov/1VCYayA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFAytkTxBCLHeTHfL3r65icUP23luXn00hOc6LKEhu3fAiEA7bYlkxnw8KQu9L6kz5aiKg09vnsP6sPufLs8KUGO54I="}]}, "maintainers": [{"name": "naitik", "email": "<EMAIL>"}], "directories": {}}, "1.0.4": {"name": "makeerror", "description": "A library to make errors.", "version": "1.0.4", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/makeerror", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-makeerror.git"}, "scripts": {"test": "./node_modules/.bin/expresso -c"}, "dependencies": {"tmpl": ">= 1.0.0"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/makeerror/1.0.4/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "makeerror@1.0.4", "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "97ad57bfd7d95669513a4a22f5bbc275c45c61ab", "tarball": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.4.tgz", "integrity": "sha512-CsDSkBCS9VJJWfucRZhrxrc/hX0iTJVI4sWBlA+1ikLvY9TKEosvw4Pi6qRymoMxsLPu5eUB6p1y4pK6WJhEvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqe2VwJiWBmM/Wf76XIRkjs9Jc2GnRSa5sH5prfEOCZQIge+qGoFeOs5LxNqanTwWW8kK7wthK0EjXsZ03Gxg/P3I="}]}, "maintainers": [{"name": "naitik", "email": "<EMAIL>"}], "directories": {}}, "1.0.6": {"name": "makeerror", "description": "A library to make errors.", "version": "1.0.6", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/makeerror", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-makeerror.git"}, "scripts": {"test": "./node_modules/.bin/expresso -c"}, "dependencies": {"tmpl": ">= 1.0.0"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/makeerror/1.0.6/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "makeerror@1.0.6", "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "e9e7a1b64b8f0190ce5dea6d813accfe9c9b08b7", "tarball": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.6.tgz", "integrity": "sha512-N3DjSbTicdayHbFton0OqLmfx0AlrJ78uDwYJ3C68lCpMRObL/jWvhuJFYbxnlL1Ool9wpekPzyegTSnUk5chw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUR9Bqp6FiYT5CyhuUwtlGXK0xgKrM5e6YDuqEtoA92AIhAJR3CpKGC0/y8DqpQvtXdfCxV0RdZRsCtoh9eF908iet"}]}, "maintainers": [{"name": "naitik", "email": "<EMAIL>"}], "directories": {}}, "1.0.7": {"name": "makeerror", "description": "A library to make errors.", "version": "1.0.7", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/makeerror", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-makeerror.git"}, "scripts": {"test": "./node_modules/.bin/expresso -c"}, "dependencies": {"tmpl": ">= 1.0.0"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/makeerror/1.0.7/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "makeerror@1.0.7", "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "f2c7f0eb357e279d246bb71073ed3223a1580aec", "tarball": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.7.tgz", "integrity": "sha512-uP+WeBFvsZRAUNQGzvKVIS9oluiGX+cuxPbAMmHXZK609ndKeW5WwpzduESS12Qo82DKenwnjeQycB0NBlnRSA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOA/bvhfGs5w0PiRzX5hIqcGUqGI2f2ymtEDK19MqsIAIhAKZvi1cm25rj2Z/gfdJ56FFwX3ZLIBnTbI/siy//qPjG"}]}, "maintainers": [{"name": "naitik", "email": "<EMAIL>"}], "directories": {}}, "1.0.8": {"name": "makeerror", "description": "A library to make errors.", "version": "1.0.8", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/makeerror", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-makeerror.git"}, "scripts": {"test": "./node_modules/.bin/expresso -c"}, "dependencies": {"tmpl": ">= 1.0.0"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/makeerror/1.0.8/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "makeerror@1.0.8", "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "443ea6aa39021ae839daf5bd2190b6193258feb5", "tarball": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.8.tgz", "integrity": "sha512-POq1fbUWw16rzU+are3BUzhLSKxT2X7KC/Tzl3L+6d9sFuDT1pnmCvmSrQarDT+g7dVhhur4Cs7ap72P3mL75A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHEDfI/9FVCZKvgnCbRWy9tecuG7b6wcVVBa/eOe53J2AiBeo6ZUlS4KP+YgetMCARQxqSXYdj8ZdDMJDmKFgxkwUg=="}]}, "maintainers": [{"name": "naitik", "email": "<EMAIL>"}], "directories": {}}, "1.0.10": {"name": "makeerror", "description": "A library to make errors.", "version": "1.0.10", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/makeerror", "repository": {"type": "git", "url": "http://github.com/nshah/nodejs-makeerror"}, "scripts": {"test": "NODE_PATH=./lib mocha --ui exports"}, "dependencies": {"tmpl": "1.0.x"}, "devDependencies": {"mocha": "0.12.x"}, "engines": {"node": "0.6.x"}, "gitHead": "f14cd6dca95d12b0f1663f31cc3eb19e3dee754f", "bugs": {"url": "https://github.com/nshah/nodejs-makeerror/issues"}, "homepage": "https://github.com/nshah/nodejs-makeerror", "_id": "makeerror@1.0.10", "_shasum": "fcfe3b0dbce733fe5ec6797ee1116b50061730d2", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "daaku", "email": "<EMAIL>"}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "dist": {"shasum": "fcfe3b0dbce733fe5ec6797ee1116b50061730d2", "tarball": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.10.tgz", "integrity": "sha512-okPsBNG6zZ3CNLvihmrZ3eHgGqjDyFoqOsJL2C3ZV3fhiJ+AZdEHoiLFbjg0BbNivH+hXy9UMya57pBorsaNtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDLzr/j5JouLIuj9WXC7WUeHu6Ru/7FxWnctGrU5RUxAQIgIrRlarN3pTXUhkWbsGRQGbs6e81li5vJ0E58oD3fUoc="}]}, "directories": {}}, "1.0.11": {"name": "makeerror", "description": "A library to make errors.", "version": "1.0.11", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/makeerror", "repository": {"type": "git", "url": "https://github.com/daaku/nodejs-makeerror"}, "scripts": {"test": "NODE_PATH=./lib mocha --ui exports"}, "dependencies": {"tmpl": "1.0.x"}, "devDependencies": {"mocha": "0.12.x"}, "gitHead": "06a15302cb11eb649a94ac0db6492c6e5eceb940", "bugs": {"url": "https://github.com/daaku/nodejs-makeerror/issues"}, "homepage": "https://github.com/daaku/nodejs-makeerror", "_id": "makeerror@1.0.11", "_shasum": "e01a5c9109f2af79660e4e8b9587790184f5a96c", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "daaku", "email": "<EMAIL>"}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "dist": {"shasum": "e01a5c9109f2af79660e4e8b9587790184f5a96c", "tarball": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.11.tgz", "integrity": "sha512-M/XvMZ6oK4edXjvg/ZYyzByg8kjpVrF/m0x3wbhOlzJfsQgFkqP1rJnLnJExOcslmLSSeLiN6NmF+cBoKJHGTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBviKvQk9avWPPJS+0ShuBMYe5cQjMUUHU0fpwHE0hYqAiAtZVCh0CjaMXUGfMqkVMNhv1OGRGOe5R/FubeDJqxudg=="}]}, "directories": {}}, "1.0.12": {"name": "makeerror", "description": "A library to make errors.", "version": "1.0.12", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "lib/makeerror", "repository": {"type": "git", "url": "git+https://github.com/daaku/nodejs-makeerror.git"}, "scripts": {"test": "NODE_PATH=./lib mocha --ui exports"}, "dependencies": {"tmpl": "1.0.5"}, "devDependencies": {"mocha": "9.1.3"}, "gitHead": "0b99c236bf5759a9195b8c8e27506828f4a6e1ea", "bugs": {"url": "https://github.com/daaku/nodejs-makeerror/issues"}, "homepage": "https://github.com/daaku/nodejs-makeerror#readme", "_id": "makeerror@1.0.12", "_nodeVersion": "16.11.1", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==", "shasum": "3e5dd2079a82e812e983cc6610c4a2cb0eaa801a", "tarball": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz", "fileCount": 5, "unpackedSize": 6073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2ymuCRA9TVsSAnZWagAAXQ8P/jxlYEvRtlCFrkM9Kp1j\nhtOTEiO5iTFGpQcUvPcOllzzEsUSnkkg5EBYWFsr5Dy4ORqshlg22ISEvUNT\nNMd2zUKO7hcdbDpr6AHAAjs8s/IJ0Dx6rQb5JHgXBEccdbdQb19je/9ejVIj\nBTcGZm/pPM4A0fu1hpEv2VSOS7eO0+ZTUzHib+BXC/xrtBtlxJp5IJyO8B+U\n4gQKtN5k5xlQdBiXKChyq9w37ZgqwgpSCsfonLRRaNprTgZA+C7vhMIvRlFg\nD7cTOuNoCt6NzNQbCZ8PrAIFprJ7+AgyMKkYpDU/v/4FaU8fA+xDnBzHxP2b\nAMNCls93t5t3br5eKtGPF7zcmYgtFKsP1V+kXVpnuPR3IAfC8YxyvZJqyj0V\nzSCFql6LJN2xvSgbGtNxzZ2FpCW4qYivUu2OMsRBG7Nd7DRC/roTDrd5IulX\nGFY5vu2QaBsZ3R6sjbAIcjCDvHmbvCdRb+sb8nK/oPqubXxgAYlq7TlkecVJ\nmpDmrfhIC7sdpKs2GdxYhkTtNbZZC0/tmIqBjEOAeRo9f9Ckx/E7TVdeGvbv\nqbhYiKf00ZB7/7W2K52vffnoe+3RP5euaDoPaJxdD7TF4VR3oOV1ib9f/2ur\nR5XFFP11pJtuqdufOhLYyszK4Kyx3nEdBMc3ueGqmT8riQQK2cquNlakqlSd\naPUU\r\n=8cTT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDaZes9x0PykYRNZ7JeMB7Z8ci1R1bCXPWskoChhLzjAAiACboef8cgbjHxbZ8o6Gwx656gghQzD+WfL3WiQ/hiXaQ=="}]}, "_npmUser": {"name": "daaku", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/makeerror_1.0.12_1634997639356_0.8540399681471622"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T15:35:20.945Z", "created": "2011-07-24T19:31:22.107Z", "1.0.0": "2011-07-24T19:31:22.640Z", "1.0.1": "2011-07-27T17:18:02.013Z", "1.0.2": "2011-08-08T01:45:42.619Z", "1.0.3": "2011-08-08T02:39:17.143Z", "1.0.4": "2011-08-17T04:12:07.567Z", "1.0.6": "2011-08-17T20:32:28.284Z", "1.0.7": "2011-08-17T20:36:05.662Z", "1.0.8": "2011-08-17T21:03:48.622Z", "1.0.10": "2015-03-04T18:09:39.616Z", "1.0.11": "2015-03-28T18:31:38.982Z", "1.0.12": "2021-10-23T14:00:39.504Z"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/daaku/nodejs-makeerror.git"}, "readme": "makeerror [![Build Status](https://secure.travis-ci.org/nshah/nodejs-makeerror.png)](http://travis-ci.org/nshah/nodejs-makeerror)\n=========\n\nA library to make errors.\n\n\nBasics\n------\n\nMakes an Error constructor function with the signature below. All arguments are\noptional, and if the first argument is not a `String`, it will be assumed to be\n`data`:\n\n```javascript\nfunction(message, data)\n```\n\nYou'll typically do something like:\n\n```javascript\nvar makeError = require('makeerror')\nvar UnknownFileTypeError = makeError(\n  'UnknownFileTypeError',\n  'The specified type is not known.'\n)\nvar er = UnknownFileTypeError()\n```\n\n`er` will have a prototype chain that ensures:\n\n```javascript\ner instanceof UnknownFileTypeError\ner instanceof Error\n```\n\n\nTemplatized Error Messages\n--------------------------\n\nThere is support for simple string substitutions like:\n\n```javascript\nvar makeError = require('makeerror')\nvar UnknownFileTypeError = makeError(\n  'UnknownFileTypeError',\n  'The specified type \"{type}\" is not known.'\n)\nvar er = UnknownFileTypeError({ type: 'bmp' })\n```\n\nNow `er.message` or `er.toString()` will return `'The specified type \"bmp\" is\nnot known.'`.\n\n\nPrototype Hierarchies\n---------------------\n\nYou can create simple hierarchies as well using the `prototype` chain:\n\n```javascript\nvar makeError = require('makeerror')\nvar ParentError = makeError('ParentError')\nvar ChildError = makeError(\n  'ChildError',\n  'The child error.',\n  { proto: ParentError() }\n)\nvar er = ChildError()\n```\n\n`er` will have a prototype chain that ensures:\n\n```javascript\ner instanceof ChildError\ner instanceof ParentError\ner instanceof Error\n```\n", "homepage": "https://github.com/daaku/nodejs-makeerror#readme", "bugs": {"url": "https://github.com/daaku/nodejs-makeerror/issues"}, "license": "BSD-3-<PERSON><PERSON>", "readmeFilename": "readme.md"}