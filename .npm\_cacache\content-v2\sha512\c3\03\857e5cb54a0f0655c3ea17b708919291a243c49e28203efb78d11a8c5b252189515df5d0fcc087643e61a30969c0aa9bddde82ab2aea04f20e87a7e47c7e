{"_id": "@discordjs/opus", "_rev": "18-83ebaf1e9001dd4d221016dcef0ca2b0", "name": "@discordjs/opus", "dist-tags": {"latest": "0.10.0"}, "versions": {"0.1.0": {"name": "@discordjs/opus", "version": "0.1.0", "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.1.0", "maintainers": [{"name": "gawdl3y", "email": "<EMAIL>"}, {"name": "crawl", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "dist": {"shasum": "0178d6b9c8e6fbbeb167108254d7fe7eaafdea36", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.1.0.tgz", "fileCount": 400, "integrity": "sha512-VWsrsgqSktxOEHx9SFcnOWJhYQOsyzaW3JVN73LDSzKrycEjtRIS+axlm4qeMGx60u6RH1SY15b3pl5G4+Y+gA==", "signatures": [{"sig": "MEQCIF54H7XUynBqFFx5mEciDFkBQB7QqOknECqhkBDYE/GtAiBpqDQWpX77CzzF1grVXTjaWEjRzn4EVWjzYAQS9P5auQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5107745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeH4OoCRA9TVsSAnZWagAAxj4P/ipfkh3NCKBs5MWdv1Ba\nve59V71+Lq7yS4120B18HV7XpaInLVbWk4e8oKpvT5jVWa+mYIOXHzPoaMZA\nAo+ZIcIXqchuxh75rowmtLzKJuuYXLTNvmelqBW0SWxxSiATLx57PEoawQBc\nJklYK3X0UWfiVNwXMqZ7Hg84wyd2zzFH74+JNT/Aost9Q1W6FJG9GL9Kd9ZP\nZveU8ThKMSQelLCEOKbEu2zMW0mGXHCq1yNUQFbsIeVrEV5Iu8auv/QKJCRW\nzik/89NQ8gW2zlilhz0DXBqO03NAnuez9Oa8Sa5IUlbaVGNxfNWuBnlhNPxV\nTVR0aR6KBnn3stadPaJjw0IJgm0rrxygR/EovLZvyp7+7S4/7CJuul/aYtf2\nbhzpDhF4Bh/vEBK1Ma0e0v56BfJltnVj5eyhZlqHVgxjyDXo+ez9JXeoLufd\n7dDbO/m7eaHVAI8X5PW+xs4k9vZYgmFjb0ChSz/gyutxqerZ944Cub9CkNvb\n+Y2GdLYrZIwWPD73XxHJWYAtzyZd0aZdAx7KGxD6DTBy2NDme+3BxeWVBy0Y\nAYOUxDCW9eDD2cJ/jBi3ttqDE1C5jFUcM0o38Dz8YEDa9e11Ygty/ipsCoOz\nQQ3Z8lwMIGQDhpKF1Br/N09V0+p61cc+bPFvHNUEq5qaeuliK4dBIWcrkaod\nCZLo\r\n=Tt9m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-{platform}-{arch}/", "remote_path": "v{version}"}, "gitHead": "1de32e180f9d44fc0d34d7436224d90d11e355f2", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp build package", "install": "node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "deprecated": "no longer supported", "_npmVersion": "6.13.4", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"node-pre-gyp": "^0.14.0", "node-addon-api": "^2.0.0"}, "eslintConfig": {"extends": "aqua/node"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^6.8.0", "typescript": "^3.7.4", "prism-media": "^1.1.0", "eslint-config-aqua": "^7.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.1.0_1579123623591_0.021049341474694172", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "@discordjs/opus", "version": "0.2.0", "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.2.0", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "gawdl3y", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "dist": {"shasum": "a30eede1b2e04a22e2d56e0ec473718dceacf823", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.2.0.tgz", "fileCount": 410, "integrity": "sha512-qkoLTuGy9NAUcy4fv15SyVbwg83+x5iZ1rytJy5nTpeMqzIT57P2X0zb+iDV1Jv7DU/pDdyqNav0iRsulyT2zQ==", "signatures": [{"sig": "MEUCIBPQK0ogql80qWGEuT8pi9SWimldEjSBlRRYQKdu/fuLAiEA63SGk1b+hhI/hwRSaTRNE63gdbBso8Aj1wNx9IzbrN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5125786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepJpfCRA9TVsSAnZWagAAtJEP/0Vke4Pg+UBa09HsuF0g\nLV7oogahujvjDxAfs1H/07vPZLn11gi7YGI7qZFlVMw7VIrBl2D+L7T8UGHR\nj6cUSQowzS6xzVuLo0sppZfDqn+LbGDmehkMOX8NvqzM/Tv+gQBQa2mlfDbM\nWJWqmE3ulYHwB3CQx4SPODvQMO5xtWYTBTVWf+JZWcbFMkGzYtcRER/U+cDN\nI0OUzJksffQxeSzcZPjB4tmnUVoee3PYx3y1emq26mWfauhdn9TIYct82CF9\nRLD5URwjUqS99ouJ0S9COKEOYcuYQQu1HQ9m3g/hPfCraHg5NOYwBlESV8TW\ng2Sz9J9bp+d/E/7gmS9FWCa8AXEkq9IoFDthrxQnKC7yMoLwDAnBfs9KcYp7\n7l3iO0tQUU8fSwFHfsDGX9lwc4A0JaUja1+KwUHzOQqHFE5g78xBHrU2iQjD\n2drYHJCE2UleFBRSOIFDOie543HkMoSpYVVawiFIQJMnx8cI+ywWlYsR1ipv\nyWC/2R8U1t98Wh1o5tHXWX+TGPLV8Nj2ozI5XFt0vYN7QPphWtpOq7Q+um0K\nvKmmB0geY2o6dmnvnpf77EW8t5+/Md2/DdqYRQ2Lr/Pc/FZxNUotTk3u9yV4\nIPuCKcz1JrIuhRTQRNtYT58HMKhg2lfeQwX6mzgSoubOIzgpf/yXSYXUfPQp\nvxYU\r\n=5BK+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-{platform}-{arch}-{libc}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz"}, "gitHead": "376b60d44a13a82a1f87c6be77cafc72aa774783", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp build package", "install": "patch-package && node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "deprecated": "no longer supported", "_npmVersion": "6.14.4", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"node-pre-gyp": "^0.14.0", "node-addon-api": "^2.0.0", "postinstall-postinstall": "^2.1.0"}, "eslintConfig": {"extends": "aqua/node"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^6.8.0", "node-gyp": "^6.1.0", "typescript": "^3.8.3", "prism-media": "^1.2.1", "patch-package": "^6.2.2", "eslint-config-aqua": "^7.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.2.0_1587845727067_0.6220202520264999", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "@discordjs/opus", "version": "0.2.1", "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.2.1", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "gawdl3y", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "dist": {"shasum": "8e4f4be5ad44e59191de49b58cd5ed495d6164e7", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.2.1.tgz", "fileCount": 410, "integrity": "sha512-abcrNjP7R8pSOYBj0samfSOHHjbXZb5JOeEHLp4MTN64st5Nvf2gh24DK1b7y7ijfhX0/W7QxfapGb20s5cUxA==", "signatures": [{"sig": "MEUCICTHkUfkPlAmWUdB/fS8MZVdZrG6VEFatv1whQMH+yqoAiEAwzp66VCDmTsfXG+zdCL2r3mHi5zp5Zbl4ew7ZWePD+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5125874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepLZfCRA9TVsSAnZWagAAZLEQAISlPyJgL3jvdtwiFHYU\nos5J7cGwlflWntBrsvi7DcpnauUlhyAHWx7nV8c2cAJuarFBR2TLYetpwzkX\nuPiQX2zA47WNvwZoXs7PJVHNLGkJiPPc/zZpqgZWJEOcORY8ud80vg3jB/pf\nWBIVr0eWjz8aQJouEQ1ctWc98FvZJOZWQJty1n3qoQ20SBTNlxMe9tWNa+e0\nsbX6SH/8/p0L4+pkasZfSBUgZS0IT2ELaPJwDEyeuEk1wJsmy7atsyIq7rTL\ner7UwTbyrdA1zg6HjR6NeLYMg4AHqVdKlLCNR18krPdN+cmrEJUghraOB2ax\nliiPu9XZW5F7g4A4O2Mc7S8DWtN1jQ3czGlng0lTSzkgaBrlmDzfK3/ZcaOA\nYxlKcmtOnRzktmgtIn4NJJeH3eFJpwIaPWDjciggziqvLaCpbfnlWsyauqAm\nj/MZFs9E5KM3VUO4gZg3evXx2qG7OCTSMMGaEEuI7gozqmY2Bgh/LQQLFIZo\n/FfJb0CJ3VNdgSrmIUoLyK3FFf2spx6yKoRyhmydMLnvIXtbhEFlU4r/TsrW\nxo+oftR43P21p7Vrn63gZYK7Q+XcJIODmGScl1ccW7r1bJIjhnBnPPfdebAI\n+wIaCwq4jp2GJwE+fe+eOs0Z98sDDzOXs4OLe3gIuqQ6+K7x8epp6/S/nETh\nB6e4\r\n=mYR7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-{platform}-{arch}-{libc}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}-{libc}.tar.gz"}, "gitHead": "7900ae3dda2e3167e6ad8fb5fa2369f6f6fd80fb", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp build package", "install": "patch-package && node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "deprecated": "no longer supported", "_npmVersion": "6.14.4", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"node-pre-gyp": "^0.14.0", "patch-package": "^6.2.2", "node-addon-api": "^2.0.0", "postinstall-postinstall": "^2.1.0"}, "eslintConfig": {"extends": "aqua/node"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^6.8.0", "node-gyp": "^6.1.0", "typescript": "^3.8.3", "prism-media": "^1.2.1", "eslint-config-aqua": "^7.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.2.1_1587852894720_0.4962252696083702", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "@discordjs/opus", "version": "0.3.1", "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.3.1", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "gawdl3y", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "homepage": "https://github.com/discordjs/opus#readme", "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "dist": {"shasum": "81c62bf723a7448894e3f5432d697228f6769a71", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.3.1.tgz", "fileCount": 409, "integrity": "sha512-ZP2t9XZyPhS4BkyN0SqCfGJNFkLvudFzc27KQDlCSja9a5daRzdV3x2AGs/Oy0vQv15h9u6w40j2H8xRpVyjXw==", "signatures": [{"sig": "MEYCIQCDf/EADgYyUrImOUCVL2LnzQl2dSigZWJkRYLHPDqfkwIhAK0eoXP6BXCbPONNwWe1Gl1OODX65DGWH5AZpqa/cvC1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5120843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerZz4CRA9TVsSAnZWagAAzswP/RHaKS/HjElmiMxbyScp\nWk/5J9kxcyz4MKtZkrXrmnb3ym8ak7GXDQ0EedzYTSWL1UagKYpJsv+Wtg1A\n8aTbo4NPb3NP5zQwobpK5j7Vk2MQQTMpCjINUWFpkVfMxbv1y9r6oYwsfids\nqcBHXG9u30DeWn5VNwCxUdGP5YxjKWTD14DRoCXtu+qnp6/k50MsKlsOTN0t\nA99ZG5eNISlLpF+DUm7YbU+sCy6Rlw2O5cqoD4TwwP7YOIZBVjPivPNybOYu\ns4KdfooouquTi2SC+33YvLBaLnrIi5+d7XcQrmbix7SA7g+EDLpQCKLS7+gH\n/hX7Szsezh+uwJEouGGJc56SKKXDTqKT8mU1ZII5lKX91bocOPZ1wYFqyQlT\nX3iEpIrp9bcAr0sx+uU5Jx2fl6m+MSj5EE0hn6yhyUSrK7D3qzbq1qJx54z4\nsBavYhBAVQzCWbv4dVJX0AhxMZVbe0zGPakMEPNrVDtBwL7uJKrze69Gqb+h\n0kvEZm6vns+9mSHtPn04fiAGx1zIdbyuJv54AKnfXSoK/0V/AYV+yjtm2AGO\nZHL65juFhh8vx+FRvElOI4IUl92ICGuRwdPXwbikRuSwOBvdfPXx8N0KdDUy\nwRnUjCGbVnsaVZ5G6GUG9V2iXpT+4nJ4rLn+c3btjP/TcER1xUpQ/fsut+lr\nL2GQ\r\n=SAgM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}.tar.gz", "napi_versions": [3]}, "gitHead": "fd1db5657a38c890148607bf0b2a195739bf2af8", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp install build package", "install": "node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "deprecated": "no longer supported", "repository": {"url": "git+https://github.com/discordjs/opus.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "14.1.0", "dependencies": {"node-addon-api": "^2.0.0", "@discordjs/node-pre-gyp": "^0.1.0"}, "eslintConfig": {"extends": "aqua/node"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^6.8.0", "node-gyp": "^6.1.0", "typescript": "^3.8.3", "prism-media": "^1.2.2", "eslint-config-aqua": "^7.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.3.1_1588436216392_0.19399840400292323", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "@discordjs/opus", "version": "0.3.2", "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.3.2", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "gawdl3y", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "homepage": "https://github.com/discordjs/opus#readme", "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "dist": {"shasum": "7dde2d4541ccfaa451e029f9d8da980286f9f56a", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.3.2.tgz", "fileCount": 409, "integrity": "sha512-T/ubykd4Xi19vDfw7/Oe+iTQBJ28yMI2PQAzK+Zue0PB9OpEYaeIXU7k5255061RwL+rHX9w7P/P2of+x2Vu2A==", "signatures": [{"sig": "MEUCIE6fvsQ2+0B6127zN+rS/WBGPPkUBsC7PdC4UbkC9JR3AiEA0hDB5kb9nLqZkRkUMWuYGmpfaBNK1zVybpwWJaTJxPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5120854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJert4lCRA9TVsSAnZWagAAD1UP/Rz+qb3rkkz8z/XPS7vf\nIBdpBsJsqc/68akkxgtJmJWM0Kl4lf8s+ZjnoKew9n4p4DRew1hHGDWutgAD\nEDzqAFlz6NF0441U8wG0bxfSwuxVqRznuE62EI6sahzbOPBVPxu5cEJhRkGI\n1cL5EH6TkOd59d4buP/qTdPQHBzuZwj3ez9wDrNe+cEPLLvv6AUmdAaacKc2\nJgetCc9t0tQaoQ58mWkE/9RaSMtmXjw75L8HD5QOhVSqUDC7z6J5m4jAMvrp\nnLiaSV1ycuGFcohMYjWzUdGglQ9EvXsSKNhRRu/NSvBvadW55hsQOrnf2H1P\nzax4EC42qhlJNjEtM8GLfG3Kia9i1OTnylmB+2y997Afpmok+a7tP2YQnAM4\ne6rgQRRJL0OeG9TW3JvLtWLFIz1HAP1/92hA20ls5ZRBYE+ocneUitR0j2wr\nACsvzonmuSC0mPA8FuFRDdEfXdfS3YaCq6x9p3hARrYcl898H193fZ2MJxgI\n47t9T7wF5j1n4Blzpd6BVjEGyn0lNdFB1UjKYysbpDKTC7L2kXwxh9cRhljH\nRUFWkYg8Jfh76w5BZ+vn2nu8nWI1a8YfHHgGFlApv8Dy2lJAYu4IQM4k2keu\nbRLF/wF4e7waws/2M9HMTYphZE6HO9/J3S1bolwzMFCeKrJzExIR2oS1eRX+\n3H6c\r\n=h6DZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}.tar.gz", "napi_versions": [3]}, "gitHead": "4760b6c6a150973ea6d791f18d80f695499a38bc", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp install build package", "install": "node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "deprecated": "no longer supported", "repository": {"url": "git+https://github.com/discordjs/opus.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "14.1.0", "dependencies": {"node-addon-api": "^2.0.0", "@discordjs/node-pre-gyp": "^0.1.0"}, "eslintConfig": {"extends": "aqua/node"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^6.8.0", "node-gyp": "^6.1.0", "typescript": "^3.8.3", "prism-media": "^1.2.2", "eslint-config-aqua": "^7.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.3.2_1588518437171_0.21223694289804573", "host": "s3://npm-registry-packages"}}, "0.3.3": {"name": "@discordjs/opus", "version": "0.3.3", "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.3.3", "maintainers": [{"name": "gawdl3y", "email": "<EMAIL>"}, {"name": "crawl", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "homepage": "https://github.com/discordjs/opus#readme", "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "dist": {"shasum": "d3a9165b817ca477164ee28315aef259c1cdab48", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.3.3.tgz", "fileCount": 409, "integrity": "sha512-n3hUs4RY0K8cz8vJ2GiZE4EdJlexoxs1vGsaft5lZlAnU6wsZfncpv4px+HVOMKN8ddtjriGfsrG4lVkAyZcmQ==", "signatures": [{"sig": "MEUCIHd7JLMv4Bq9A/JPpVqUK/3L4QfOUrgBNK2bT45am1ezAiEA5gSbrQk1l6Hc+bQfnKD1YtHlSlY1CSeT/MJjHlJlwsU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5120929, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfla+2CRA9TVsSAnZWagAA0OAP/RPONlCHKPb9J+UVt+vM\nm4WyDxNFXC2i2p0SxfFLJ4Jv82r5czQAPzQNaQaMMFIR0jn1NNV3S4rAUGlN\neyZroLOKKLSZGA/73vfAtEdDa1IoBXVvnpDhuhN68YpHxkFW+jzbGS8ZrCRA\n144DLCLURRgLmz7LZH+fwv7gcrtICYBMLJaMwxogKi5rTBGL5KM2wrVyjdWz\nZ5sgr6Vcd3I7Uek/YKZxwCBNgGarZ/bvxhB7oebMePbce8ljy43SEiWFOo1f\nMFWzo28NccYVzr5WM4mdeHphPZn+myPtCx+cxr7qlVEcSHc7l82y5oFsWBvf\n+gsNkZmXKs0n9Yx6mxOP6NkabmUG6fnNzru3KlE2QFHWjZwpfatkjELRg4W1\nFpvGoLtnu3bcxphF1lO+STC/Dh4qXW3ALYNIL9izrsCKfowffWkm8h0SMua0\n0FBvrSQmlyAEZhRFkHf4zYWZx27NGe3vyWk1r22ZTfmluKu1J161HHU37oXt\nFN6uxHWMLuZKkPk77WROUzxntiCvm7F8v2RzpyfUuUxxyjvkQXBij0y/HFmg\nNAz5NlAmxYAow/N9wwYGEnYH80mHsMVmDbR8oGOQhf0aK7nGoT9wopzBplpd\nK7QckJeO5cjaMDTw0ZljpxNdPsmA1p5lBHkO3Zk2T1it5Armf73xxle5Z30k\n9Q0L\r\n=K3jf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}.tar.gz", "napi_versions": [3]}, "gitHead": "abe081b918ddfed18264be604cd1f5bcab1a062d", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp install build package", "install": "node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "deprecated": "no longer supported", "repository": {"url": "git+https://github.com/discordjs/opus.git", "type": "git"}, "_npmVersion": "7.0.3", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "15.0.1", "dependencies": {"node-addon-api": "^3.0.2", "@discordjs/node-pre-gyp": "^0.1.0"}, "eslintConfig": {"extends": "aqua/node"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^7.12.0", "node-gyp": "^7.1.2", "typescript": "^4.0.3", "prism-media": "^1.2.2", "eslint-config-aqua": "^7.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.3.3_1603645365787_0.7521996961738087", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "@discordjs/opus", "version": "0.4.0", "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.4.0", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}, {"name": "gawdl3y", "email": "<EMAIL>"}], "homepage": "https://github.com/discordjs/opus#readme", "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "dist": {"shasum": "3ec5fda1127fd2e540f1c70966ba8ae245da0969", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.4.0.tgz", "fileCount": 409, "integrity": "sha512-89PRSuvBKBvZLkftVqRnQ9/rPXG9zDjKdbtlGC0RiZ6hJYFdWb4ND/maMgJPkTAvlIrRChm67eZOuIYhiKBDbQ==", "signatures": [{"sig": "MEUCIERlh7vk2x2QPFDBfg2Agiddq2nN1duyGys75Se7xUAzAiEAjqadljCpPPxbA3hYeHnyUKijJTPmfSctVmKpY77hfSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5120831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETJGCRA9TVsSAnZWagAAAMEP/jq0Ofnv6GSeHhWC4mfw\neCTLOus2der+nlxcZuUPrJtfaTUiUO2izQjnoO03DylWn2xyiUFl01KWU8rj\nOmzZ99jIFSfVNljfpfmu7UWku6Ek2er307ueDeVQNEeohNnbBC4dGaXNNanG\nvBKKqW7noaRBmMWTTYHDo7qey7YC3QvvhP7fzdTXRUTY1D5fYQx3QkkcGFLg\nMExDn3rMknXRfoe0vozqlNjNoyBjrX3l9aKrpnND86rm+nwp5zQEoDm16Zzv\nXauEIbyZBQFId5DZDSQm9sUdrOlSu3270XUVC6EqLgSjKetmzao16r+SkS0U\n2yk4/TO9LQ8TfeLx+JeYxg6PAG31/N2NCyccFfl7OETModyb2F8wUbDKRkir\nDa4/RuSOn/Mvi+NFWFYnxRTf/AfmDxG3i9L6RaN5kLlrUxpdoDqvnukPC4Oo\nx5Lae8aKjczSobQAX9gYEbvj2XYJyQQuTZAyW83dhaukTt4AlNSuNOFYtlni\n8Xc3peCBAJT+/KDolC5dK5KPS7nOB0ZHmfChtJ6MBYGYnY61I14pyJaWL3uQ\ncng9Dyt3JXvT0fss6cfL7mbiuTY/+WPOQ1kxWGEF00m1o+MoBj7gQDKfbSst\n3spNOck1IKgFKweS0eKSV67W+acsdgd9BRgLhcMMJ8kja8U1HZTgeax9Wo83\nXO4K\r\n=W23Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}.tar.gz", "napi_versions": [3]}, "gitHead": "f0cb4eb7e7029d56db2c40af133d57b7cebdff19", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp install build package", "install": "node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "deprecated": "no longer supported", "repository": {"url": "git+https://github.com/discordjs/opus.git", "type": "git"}, "_npmVersion": "7.4.0", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "15.6.0", "dependencies": {"node-addon-api": "^3.1.0", "@discordjs/node-pre-gyp": "^0.2.0"}, "eslintConfig": {"extends": "aqua/node"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^7.18.0", "node-gyp": "^7.1.2", "typescript": "^4.1.3", "prism-media": "^1.2.3", "eslint-config-aqua": "^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.4.0_1611739718161_0.11841023768530445", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "@discordjs/opus", "version": "0.5.0", "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.5.0", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}, {"name": "gawdl3y", "email": "<EMAIL>"}], "homepage": "https://github.com/discordjs/opus#readme", "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "dist": {"shasum": "26ae9f30d52c03ce1671b1db8eec957206fdf92b", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.5.0.tgz", "fileCount": 410, "integrity": "sha512-s3XUJ7dYV+UvYUiqkgs7sq8JKWMhQrHcwDECP2SKdxtL9h9qGa4mr0IR6XnZw+G/Sogx8c+HRS7+wEspkqk3zA==", "signatures": [{"sig": "MEQCIE9iyALmklC6azroxSjZyVsIcngpyLXKYcecE2ZRkZ0WAiBex+ZNEmtbiqymEq5NVYk1doBb8c2lpol009dKvnXjUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5126741, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVJjDCRA9TVsSAnZWagAAOTAP/iWozW6fK6WbxVlknlHL\nP4l6ORumRFqdwpVLt0MJbm/bZMiwqAXbbeG9+JFnLWAA7g+U8cRAUcUdbLTY\nIa44197TE4kL9TQ1w/2CkwSASEbnlFy39eaFTvwVbI/taAngg68quL9q9j9H\nMmp4FfvtkhrwMh9G8BsWS6yYBFkvv1RugKHqcZz+fCznBEktxFLG6vtP4mmr\nj/zG7hZnUP8H2OJVD6VzdvFWXGib0J8k1+vbF9iQfy4ux3cUJJ4YdpmECxnk\nC6KF3ddcWZpSR9+6G11K42OgVJqjdNFSt39wR/EYgsdvPuVC4H+EeErsCZh7\noUrzEQIWTVS7Um4KFJQjiV3L23B39QIlKM13MDeJy6CYncCf8LPDC/4V74L6\nztyKi8SVdmdnmNkcymZ2NHFQZmL3F6/+47/nydyrboFoK2bAborg9zrg2LV6\nth/54X2ZAZJJMjfwGgsgjszfMdJBzKYLsbVgVqJCtouBj7ilnmHsb03vjBC9\nw1d3iOwWjQCukbuKALea2lhg+PJtg2smtAUKRWIZSMwycV2wS43uoSqUKmW9\naLIdcS4Eb/EdSDSBBMMCaEka6d5DTMnbyhxZKEkbtTx3P6FSUjTni2YAjMNJ\n79HERvFDsjzO4vEpmp5kuD0r20jKr1K1Y1q2sp9bJu+4WCUiJgYtbL6DvdLN\n1+ir\r\n=P7Ak\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}.tar.gz", "napi_versions": [3]}, "engines": {"node": ">=12.0.0"}, "gitHead": "e5a690c9268b899e796345f3116c3799d642e814", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp install build package", "install": "node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "deprecated": "no longer supported", "repository": {"url": "git+https://github.com/discordjs/opus.git", "type": "git"}, "_npmVersion": "7.6.0", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "15.11.0", "dependencies": {"node-addon-api": "^3.1.0", "@discordjs/node-pre-gyp": "^0.3.2"}, "eslintConfig": {"extends": "aqua/node"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^7.22.0", "node-gyp": "^7.1.2", "typescript": "^4.2.3", "prism-media": "^1.2.8", "eslint-config-aqua": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.5.0_1616156866572_0.6150865933848493", "host": "s3://npm-registry-packages"}}, "0.5.2": {"name": "@discordjs/opus", "version": "0.5.2", "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.5.2", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "homepage": "https://github.com/discordjs/opus#readme", "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "dist": {"shasum": "480584820a3e67ad26d049c1ec5e88ef9139e69b", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.5.2.tgz", "fileCount": 412, "integrity": "sha512-IUJIpEM7Fz3frjJsc0Rrq54CycuEZH1t3RKW6mjlBCz+bPa/Gavip1pvfr015nIaONLqnj5xde8DNIxGvhIhMg==", "signatures": [{"sig": "MEQCIG2XO1SF9BM6hoDT69cqfcL3NmDiRqbxzRFEKEh4HI1VAiABhvQf9j2ChKmpsRg9MA8zs6skfhfoNrPDkP/kJ2mPug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5126913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwitfCRA9TVsSAnZWagAA7WwQAINnfc4+2D+jajt4BFI1\nuSq28GkG53mQlcJGpGdrMwVbX8ChU3pvMKO0ts4Cq+gcMN7uPITNC1GbjGPs\nmkESt6A/WmN8VwAeO3X4+g2MgsUz/I38n3NyespWRQxD3kNA6odsvFdf9ler\nR40E1lsdCjTwvhNWtkMbkW9t9hIqYdbOk38R21CjiALcOFXsm/QjModhy52y\nbZpksxKUEJKLV+j/JPQJZeuYR5Oq8O4+6CQ+R5+IKBAfljXDNA5EVuW3F3Ok\nObU2uUOX2k8fdDP9ilix4ouNFmHkr+XEq0shJL7wya/AzMagPpPc6FP3nClZ\nKanQNXYcCLyCUD784NGqHFSqXcAFVsxJ42L5WPKB/6biWYrk+Y7SM87EOXKH\nnwZXbUvTTq8jFhO2Uy/Os5ZyilzLiq+EatU8U5J9ILmZSNThvfa2eZlegPKh\nhvzid1n+5TwORGPPjAEyKGw7cQSfeDMDFP8M856b4d+/Aik0V1u7TOgXx1Nc\n5tKKosJY6PqaXf1nE3+ZQhZdB+1m6pZNvbKSFJeb4QCJ92oBZ9CjHKwzwDnx\nPRIR0no3p3J9lY3n3MkSoEgCUEw6juNqufjQvpogSPDMEr56BCQMiNc5MK6/\ntc8E+PntCXOZ6hbqdJtgD6g1d04e2PaKeRZvPmslHBLLDaZI+ZIdWo1NJNWs\nPHKo\r\n=T1Cn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}.tar.gz", "napi_versions": [3]}, "engines": {"node": ">=12.0.0"}, "gitHead": "22fd6b743154c32e97c7a0d0504cb5acc156bdce", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp install build package", "install": "node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "deprecated": "no longer supported", "repository": {"url": "git+https://github.com/discordjs/opus.git", "type": "git"}, "_npmVersion": "7.16.0", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "16.3.0", "dependencies": {"node-addon-api": "^3.2.1", "@discordjs/node-pre-gyp": "^0.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^7.28.0", "node-gyp": "^8.1.0", "typescript": "^4.3.2", "@types/node": "^15.12.2", "prism-media": "^1.2.9", "eslint-config-aqua": "^9.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.5.2_1623337823017_0.5020718353949154", "host": "s3://npm-registry-packages"}}, "0.5.3": {"name": "@discordjs/opus", "version": "0.5.3", "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.5.3", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "homepage": "https://github.com/discordjs/opus#readme", "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "dist": {"shasum": "7294f539da0b37bf13b072e51acb47ffca748471", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.5.3.tgz", "fileCount": 413, "integrity": "sha512-IQhCwCy2WKXLe+qkOkwO1Wjgk20uqeAbqM62tCbzIqbTsXX4YAge8Me9RFnI77Lx+UTkgm4rSVM3VPVdS/GsUw==", "signatures": [{"sig": "MEQCIHKKgZtG2ebFZu308iE0wlj+nv/nZvfKqaZ1ddNWg0EWAiBEkL5GvEl/p2p/5tcAOmRuB4XwUba2BAgebLP3oTj4rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5132808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwjyxCRA9TVsSAnZWagAASN8P/jT8mrKtXiRyRNbqCdCL\nrFLAtXypX1rAsjpJlYGzdFydwzpi7/Ch1Cf/7ZxBMPUw0mCoVOA7fEre3OMc\ngKxVOdEn9ZaxtY/qp9lwUEKt4SeEOHvCgVA2w7E5GKh110FZa4rld8OAjw7K\nSD/RIL7dnFDXHBdDA+whEKno0SxSoYTLq4thyLS+s5ey/cf00J2Au+ARN6+k\naB4n7GtwVfQscbkj+NYKJJukpKwRkwre7UIbQh85hJ78+cbv0y1J2Kstepxr\nnOFh1ysGmOWzHTHOlYrh0RlenuByLQUSbBefDHLGoViml+2VRZPu7e32qslh\nDq+9+LzJh2r6SYDN/Tg+MlOJZEmqwhcqGimvROMR15T4HjEk5f8KxYc9/cKc\nV1v2a1yIt/rddwPf4pLqMo3I2uLern9UjscKHPQmAQSwvW+QDANXotXvnGXZ\n2SDyCOw+beWlq21kzoFx4HUI/40ip/iV1zx1aGnbaLtU3jtW91luTzD5MGnJ\n16nHEORbv8+EH6gZ4QpAmwha5YhviILClcmqy18z6g6a5S9m5aYBe2084/XA\nNoyPkAcdl8i5lnXYHCmS7BaaNkd2IjKPATImBUHtCkdWG5NFJfQ7f2n4v5x0\nnxATQx23bUuKlWLzLeLaOO7pb9vSM2qb2jePKrlfMffkyKQWrG7YVaKsrE/W\n3Bj4\r\n=BSzv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}.tar.gz", "napi_versions": [3]}, "engines": {"node": ">=12.0.0"}, "gitHead": "3ca4341ffdd81cf83cec57045e59e228e6017590", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp install build package", "install": "node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "deprecated": "no longer supported", "repository": {"url": "git+https://github.com/discordjs/opus.git", "type": "git"}, "_npmVersion": "7.16.0", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "16.3.0", "dependencies": {"node-addon-api": "^3.2.1", "@discordjs/node-pre-gyp": "^0.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^7.28.0", "node-gyp": "^8.1.0", "typescript": "^4.3.2", "@types/node": "^15.12.2", "prism-media": "^1.2.9", "eslint-config-aqua": "^9.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.5.3_1623342257424_0.9265243075749978", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "@discordjs/opus", "version": "0.6.0", "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.6.0", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "homepage": "https://github.com/discordjs/opus#readme", "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "dist": {"shasum": "2afe764d305945ea07fb150fe037eb9fe2e45d13", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.6.0.tgz", "fileCount": 413, "integrity": "sha512-Xuw8OP9BJgObEBs/SgxixnKj+y+w9st45uOAJ+cGegUGwxAIR4J7sQt3Y1J3G9SGGe82+MTxvy7sg1/QUkigcg==", "signatures": [{"sig": "MEYCIQDV4F7NuR1T9dCyyFRptYIsUs4tr02lc/lRMp2WSvnp5gIhAN4xhALnVTkUV6XoftHORHNeGGFaRqX+cshq3ITEjsEK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5133385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHNY8CRA9TVsSAnZWagAA6YYP/jjXNnFT8+yEBtdE0ekU\naLNtiq8hB0Xz5jLFQL4kSWm1mWN3Mv8rJHT3UAUp7is/BRvHR5vdAMsqmJ+m\nezG0avyDDNcUq7MLwsEaryKpSiKf+rb1Hndhiw4604NtghkFrt6hXdwR0iHR\nMLw+gjvfmQfAzhtJvOR7ILklsTeNb4Ua3CURcSZA1o1+AVojzZaDI+E87eWj\nqKm7Zw4tBS++wD/EHXVmn6RSPHrXyDVrbnhZ4smmLszQ97zlYo8ekIkLjzVU\nnc2Tom/cF+qfVpbbZW0qT/DDrCax93f9GwH6Az5cllO2DLbvW2AvcSY0Gp+c\nXw/vmIsGyYRLQsliJH2ue62pDBchUSGVlKT15YaTq6JeMWUNzOf6DTfNyWmQ\nY5m1hgCzNBm4HrUyr8NtXR7SYLQqeTTzgDwqRQmMEtsbsfyz4gVq4sYuE4QL\nzBm0T1yAfc6iziE3e+gzmKnARnNa2Sf3LuFqDdlGQpe0ML5Ca8BhnEsqjGmT\ncCaBuFLu7TmlhMAh4XWzJjapBbgmYu5cXhKuhFUZfi1KBnav/GZIcQTMODUd\nNRULn0NCA3DAdM7cdAbAvLWt4Gp3LLN/WkHBOWjsN1dPjtH/43kZthtGWwtR\nDWCOs6vhZYU11AxJxI8y7TJ82aPzwm52ejowATcPtQ4CSNCJRBw1OVIpk8nO\n78ml\r\n=TyG4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}.tar.gz", "napi_versions": [3]}, "engines": {"node": ">=12.0.0"}, "gitHead": "b19e7bc22d2b932461af9deb8702aaf3626b196a", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp install build package", "install": "node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "deprecated": "no longer supported", "repository": {"url": "git+https://github.com/discordjs/opus.git", "type": "git"}, "_npmVersion": "7.20.5", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "16.6.1", "dependencies": {"node-addon-api": "^4.0.0", "@discordjs/node-pre-gyp": "^0.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^7.32.0", "node-gyp": "^8.1.0", "typescript": "^4.3.5", "@types/node": "^16.6.1", "prism-media": "^1.3.2", "eslint-config-aqua": "^9.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.6.0_1629279804748_0.4974020807744377", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "@discordjs/opus", "version": "0.7.0", "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.7.0", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "homepage": "https://github.com/discordjs/opus#readme", "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "dist": {"shasum": "69e9611a6412ea470f8e640f54478da89326ca3b", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.7.0.tgz", "fileCount": 413, "integrity": "sha512-3Xxa3dh7taSDwBAR5fLALZ/KTxvbMmHCMxYLYve6NlPO7Ms1CLmKqp/R4ZoVzkRGQVUVWEhaB1s0v9jfa2tfDg==", "signatures": [{"sig": "MEQCIBSEdfc+srTphkhgej+b95RkxvKCH1vXbGVBoexL8W0PAiAoN9fX1gKRtUd/mzSgpGFqHwArT7t3oTHEXuG2Edz5zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5133881, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzwsCCRA9TVsSAnZWagAALS4QAJTZHoqyms7uXT2Bl1hR\ng1z3uW9XGdrP46iYnq+yxzgCufHqYj1ZRZ2ms3dPalZvewpcN/7b4UCa9Jb3\nAYiA5TEu7n2lpUfaNnBxiGBdW5QP4Y90ldMddeL4BwJDDA2aJaZZVRHn7lYC\nkVPhu2TizNVut5pbCwp7PUIK9h6W7HFwp/u7rYpAoYRT3RnVUkNMUkewnKjt\nqsibWIXwj5//vBsPLhX2cPD/sZVuBP8Bve5mKQX6Tow3J7ilFWbH3jf6ZdXf\nBcZk/A2zS8IvA1UX8Rr70DKA2q/hNGfq7gnGESXN6zu7Qc0f2N/c/nHWJ5Oy\nr7hHs0GkBFtWCyHSvSXa3Qctt7LJoFovzgFz+c9D7uT7Ucaj02rn1Iw3GoD5\n+2rooZ6rl6GzqFAXitP+tucmQI3Fe2QHJxc2jZYsiomUj+jeL7VWe21BhWp8\najz/lgmDusQJ/NYFhSIv9srHmwbjUltcHWIgjqodHb89cOHVEDSIjNAeHOOj\n+g0O/ONuegAswOX8fjCPbSoD6EvvHderYh+WgvUunFcG6/lA/h9Uchol2UYj\neXlS1Jjf/Ti45xBxR0SoV610/xxl8zj+IHDJJXDf3+gN/5h5Kt+oY5PscJaJ\nLVeHfORR41du3roToIdO/kwTvdY/3Tx/aQxuRLtvq0Eo3mQskHIsvlV+kKsD\nHq+c\r\n=yYOW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}.tar.gz", "napi_versions": [3]}, "engines": {"node": ">=12.0.0"}, "gitHead": "fbd68eeca7637559bf3141b0ae76d48873b8d18a", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp install build package", "install": "node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "deprecated": "no longer supported", "repository": {"url": "git+https://github.com/discordjs/opus.git", "type": "git"}, "_npmVersion": "8.1.3", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"node-addon-api": "^4.2.0", "@discordjs/node-pre-gyp": "^0.4.2"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^8.2.0", "node-gyp": "^8.4.0", "typescript": "^4.4.4", "@types/node": "^16.11.7", "prism-media": "^1.3.2", "eslint-config-aqua": "^9.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.7.0_1636724001849_0.10079021086902973", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "@discordjs/opus", "version": "0.8.0", "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.8.0", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "homepage": "https://github.com/discordjs/opus#readme", "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "dist": {"shasum": "dae2d220a7942736343021f9af2b2b921d482c46", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.8.0.tgz", "fileCount": 413, "integrity": "sha512-uHE7OmHEmP8YM0yvsH3iSdacdeghO0qTkF0CIkV07Tg0qdyOLUVkoZHj5Zcpge9rC4qb/JvTS2xRgttSZLM43Q==", "signatures": [{"sig": "MEUCIQC3C5COvCtMW/0TA4bpKeNHfeD3QNGe8cVW86adzvwiNwIgXuU6G/6SmqqisjbGaTZz1RH6uyUD2rJJahxV03GcWHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5141764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwcRBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoAjQ/+J7J+pxJY+E+t0agBvjEHOSLQjLwk0LKz+0K4O/BjwDajhAJj\r\nkPPgRT5ZUPidA+i7JGy+tcrCRcER4oOcVbAztexcKQfiXpFw7n0CzQ+gYvoJ\r\nfk52WScwr1WeQ1CXQ/L8gVL+PBzuQhBPP1MUGQVuroCtO/7UVZlDBqHbVI6d\r\nsuGdQvu+xKso4O5vpMmjw1KpFyFUvKK8nzfF38VU1xTagbXmymfZjgQ3n+ge\r\nC/m75XFtNcGrYoVBN7gcp6eJOvjMhw3z84xVgJilV/p19zQvG8TH1PoEmhwL\r\nN4U6PpJfECs5BXUqy81KXvQYsv0qZv2hSSaimSBv8B2UTFTam2B+aEOSXJz3\r\n8XTFuuoCcoOq3uESdQTresBu+czwYfjVGESPNSTxa3m6kiSM5bW9JPnDPxOR\r\n7DJdO5XRxezd+1mm8XzLIVMgfsJqQfMe5ZdmwW0/O/XNww0+La9T3yKCFXlF\r\ns57QO2eyY/uZSG1E7NHArlBMeaw/C8e/gEOzYby3w5iNvoW712lK1ilQ9iBF\r\nd80RPv3jYSRcgtLOGBrmKV2yWQ+VOteae/rVwM+z3dos0XjveZ6RgJvkGI+g\r\ndeuXYtzvMdmi6W0HvifkoZLFokFRunNl2Ld/EQ9X7Pn1begBn9wBQqznMHNV\r\nVT8w7SNZQFi15tn4ybZXfbB1xRw0xVybRIs=\r\n=FU/X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}.tar.gz", "napi_versions": [3]}, "engines": {"node": ">=12.0.0"}, "gitHead": "478484d2482c38e3a5f5c7cb8fb2cf9cf3f2c9bc", "scripts": {"lint": "eslint lib", "build": "node-pre-gyp install build package", "install": "node-pre-gyp install --fallback-to-build", "lint:fix": "eslint lib --fix"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/discordjs/opus.git", "type": "git"}, "_npmVersion": "8.12.2", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"node-addon-api": "^5.0.0", "@discordjs/node-pre-gyp": "^0.4.4"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^8.19.0", "node-gyp": "^9.0.0", "typescript": "^4.7.4", "@types/node": "^18.0.1", "prism-media": "^1.3.2", "eslint-config-aqua": "^9.2.1", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.8.0_1656865857248_0.97497276775244", "host": "s3://npm-registry-packages"}}, "0.9.0": {"name": "@discordjs/opus", "version": "0.9.0", "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "_id": "@discordjs/opus@0.9.0", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "homepage": "https://github.com/discordjs/opus#readme", "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "dist": {"shasum": "bbd9f78bd6bc399885bcb48645c6d2b1f0efa0c5", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.9.0.tgz", "fileCount": 414, "integrity": "sha512-NEE76A96FtQ5YuoAVlOlB3ryMPrkXbUCTQICHGKb8ShtjXyubGicjRMouHtP1RpuDdm16cDa+oI3aAMo1zQRUQ==", "signatures": [{"sig": "MEYCIQDOJ78WMp+6eGwTAEopUHSGIZPGU+ck86qkEPcbW9HZnwIhANCT2WUgVdgH+Rcwq+mu45n1aNs66yVZtGeLagnhjTJ/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5142006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUMsFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwKBAAgku7DFIkk7UskzwtxiP6ExZ0e7V3+DmhB8i+VXGiQdrlt+Bo\r\nHqHt2trbprOhDQ8uK8UF+YrNsqF4seUIHBxTGBCkw7Vo3tC60TTUTXOYMnm3\r\nacNOTTr1BJNRtOwtJ2zjmkfyFKPOhwQ2JniiEKZ8VbZNIvw4IcDZTgKegZMs\r\n/Ae3cUV0F0Bmx7z5KvbKvNUN48hx014+7PTND5jYksuXlwNN/wTCsFWG/bKv\r\nSY7r7j48ozhvTN0kYBobNnHfsD0s5petpqlcM9H+2Ca+a6HkPL0Mictjut36\r\ngdBr12k0fEFOM2ri6v1+TTZeZ6EZrNR5+uQYTVAw2WYHu5ujc5GX557nq/GF\r\nNF9dzc+NN+CBj+osWbUljCiVGQaC6VJiOO/PLQJ8ntbWfu/A1k+1MW8GgkRk\r\nL/8Zb3KPUOJD4sQYF2BrkoU293nIxnaKDWCLkIJ6rQeaAjdB2gOEX0HVsadq\r\nH+RB7gjfGIvpp2LlXqj2Fh58SAAyi404Ot2AuDPkTsmaJsmEsR+2Jiz7DtMK\r\nQKFnnTC/+DFj5zKWuSnsWW3V3Nidlm7/RWriP3eUc2mCHU+7US/CdbButEeh\r\nuPxIrG/ZODfuzevj2pbtOPYkR4MHzk8dC3m0LtVGE37rORHE+E/6c0cnL3qm\r\n1QL9B7k7IVHmsXX25O/8zUNeaZElAUZ/bI4=\r\n=8pkX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "typings/index.d.ts", "binary": {"host": "https://github.com/discordjs/opus/releases/download/", "module_name": "opus", "module_path": "./prebuild/{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}.tar.gz", "napi_versions": [3]}, "engines": {"node": ">=12.0.0"}, "gitHead": "814e500c2785c5207ace19650192629beba2728b", "scripts": {"lint": "prettier --check . && eslint lib --format=pretty", "build": "node-pre-gyp install build package", "install": "node-pre-gyp install --fallback-to-build", "lint:fix": "prettier --write . && eslint lib --fix --format=pretty"}, "_npmUser": {"name": "crawl", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/discordjs/opus.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Opus bindings for Node", "directories": {}, "_nodeVersion": "16.18.0", "dependencies": {"node-addon-api": "^5.0.0", "@discordjs/node-pre-gyp": "^0.4.5"}, "_hasShrinkwrap": false, "devDependencies": {"eslint": "^8.25.0", "node-gyp": "^9.3.0", "prettier": "^2.7.1", "typescript": "^4.8.4", "@types/node": "^18.11.2", "prism-media": "^1.3.4", "eslint-config-neon": "^0.1.39", "eslint-formatter-pretty": "^4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/opus_0.9.0_1666239236462_0.004429540097285134", "host": "s3://npm-registry-packages"}}, "0.10.0": {"name": "@discordjs/opus", "version": "0.10.0", "description": "Opus bindings for Node", "main": "lib/index.js", "types": "typings/index.d.ts", "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"install": "node-pre-gyp install --fallback-to-build", "build": "node-pre-gyp install build package", "lint": "prettier --check . && eslint lib --format=pretty", "lint:fix": "prettier --write . && eslint lib --fix --format=pretty", "test": "node tests/test.js"}, "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "repository": {"type": "git", "url": "git+https://github.com/discordjs/opus.git"}, "dependencies": {"@discordjs/node-pre-gyp": "^0.4.5", "node-addon-api": "^8.1.0"}, "devDependencies": {"@types/node": "^18.11.2", "eslint": "^8.25.0", "eslint-config-neon": "^0.1.39", "eslint-formatter-pretty": "^4.1.0", "node-gyp": "^9.3.0", "prettier": "^2.7.1", "prism-media": "^1.3.4", "typescript": "^4.8.4"}, "engines": {"node": ">=12.0.0"}, "binary": {"module_name": "opus", "module_path": "./prebuild/{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}/", "remote_path": "v{version}", "package_name": "{module_name}-v{version}-{node_abi}-napi-v{napi_build_version}-{platform}-{arch}-{libc}-{libc_version}.tar.gz", "host": "https://github.com/discordjs/opus/releases/download/", "napi_versions": [3]}, "gitHead": "d757e035c33f80cdd0bd2527ce1000234664a48f", "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "homepage": "https://github.com/discordjs/opus#readme", "_id": "@discordjs/opus@0.10.0", "_nodeVersion": "18.8.0", "_npmVersion": "8.18.0", "dist": {"integrity": "sha512-HHEnSNrSPmFEyndRdQBJN2YE6egyXS9JUnJWyP6jficK0Y+qKMEZXyYTgmzpjrxXP1exM/hKaNP7BRBUEWkU5w==", "shasum": "512c26326de62612f19d6ea01cac33a4634b5461", "tarball": "https://registry.npmjs.org/@discordjs/opus/-/opus-0.10.0.tgz", "fileCount": 416, "unpackedSize": 5150342, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCCM2EcyjuXohz0J5NU25hAirZPuux2TP7sN0zOdLRDSQIgaacORf7l1L7ngTXEGp1inoqZ1QnUmiRvTqcyilyywxw="}]}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/opus_0.10.0_1737839396747_0.9898852352462078"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-01-15T21:27:03.495Z", "modified": "2025-01-25T21:09:57.257Z", "0.1.0": "2020-01-15T21:27:03.741Z", "0.2.0": "2020-04-25T20:15:27.287Z", "0.2.1": "2020-04-25T22:14:54.918Z", "0.3.1": "2020-05-02T16:16:56.575Z", "0.3.2": "2020-05-03T15:07:17.304Z", "0.3.3": "2020-10-25T17:02:46.019Z", "0.4.0": "2021-01-27T09:28:38.445Z", "0.5.0": "2021-03-19T12:27:46.821Z", "0.5.2": "2021-06-10T15:10:23.371Z", "0.5.3": "2021-06-10T16:24:17.697Z", "0.6.0": "2021-08-18T09:43:24.986Z", "0.7.0": "2021-11-12T13:33:22.051Z", "0.8.0": "2022-07-03T16:30:57.446Z", "0.9.0": "2022-10-20T04:13:57.019Z", "0.10.0": "2025-01-25T21:09:57.049Z"}, "bugs": {"url": "https://github.com/discordjs/opus/issues"}, "author": {"name": "iCrawl", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/discordjs/opus#readme", "keywords": ["native", "opus", "module", "c", "c++", "bindings", "binary"], "repository": {"type": "git", "url": "git+https://github.com/discordjs/opus.git"}, "description": "Opus bindings for Node", "maintainers": [{"name": "crawl", "email": "<EMAIL>"}, {"name": "hydrabolt", "email": "<EMAIL>"}], "readme": "# @discordjs/opus [![Build](https://github.com/discordjs/opus/workflows/Build/badge.svg)](https://github.com/discordjs/opus/actions?query=workflow%3ABuild) [![Prebuild](https://github.com/discordjs/opus/workflows/Prebuild/badge.svg)](https://github.com/discordjs/opus/actions?query=workflow%3APrebuild)\n\n> Native bindings to libopus v1.3\n\n## Usage\n\n```js\nconst { OpusEncoder } = require('@discordjs/opus');\n\n// Create the encoder.\n// Specify 48kHz sampling rate and 2 channel size.\nconst encoder = new OpusEncoder(48000, 2);\n\n// Encode and decode.\nconst encoded = encoder.encode(buffer);\nconst decoded = encoder.decode(encoded);\n```\n\n## Platform support\n\n⚠ Node.js 12.0.0 or newer is required.\n\n- Linux x64 & ia32\n- Linux arm (RPi 1 & 2)\n- Linux arm64 (RPi 3)\n- macOS x64\n- macOS arm64\n- Windows x64\n", "readmeFilename": "README.md"}